import React from "react";
import { createUseStyles } from "react-jss";
import PropTypes from "prop-types";
import { commonTokenFields, customRtlField } from "./common";

import config from "./config";

const useStyles = createUseStyles({
  '@global': {
    ".counter::before": {
      content: 'var(--counter-text)',
    },
  },
  customRtlField,
  pageNumber: (props) => ({
    extend: "customRtlField",
    ...commonTokenFields(props.pageNumber),
    "& span": {
      fontSize: `${
        props.pageNumber.fontSize || config.PRINT_DEFAULT_FONT_SIZE
      }px`,
      fontWeight: props.pageNumber.fontWeight || "normal",
      fontFamily: props.pageNumber.fontFamily || "unset",
      direction: props.pageNumber?.lang === 'en' ? 'ltr' : 'rtl'
    },
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  }
});

const getPageNumberText = (lang) => {
  return lang === 'en'
      ? 'counter(page) "/" counter(pages)'
      : 'counter(page) " از " counter(pages)';
}

const PageNumber = (props) => {
  const classes = useStyles({ pageNumber: props.pageNumber });
  return (
      <div className={classes.bodyHeight} style={{
        "--counter-text": getPageNumberText(props?.pageNumber?.lang),
      }}>
      <div className={classes.pageNumber}>
          <span className="counter"></span>
      </div>
    </div>
  );
};

PageNumber.propTypes = {
  pageNumber: PropTypes.any,
};

export default PageNumber;
