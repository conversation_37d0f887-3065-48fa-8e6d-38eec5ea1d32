import React, { useRef, useState } from 'react';
import AutoCanvas from '../components/AutoCanvas';
import { PagejsViewer } from '../pagedjs';

const AutoCanvasExample = ({ letter, layout }) => {
  const [canvasDataUrl, setCanvasDataUrl] = useState(null);
  const autoCanvasRef = useRef();
  
  // Handle when canvas is ready
  const handleCanvasReady = (canvas) => {
    // Get data URL from canvas
    const dataUrl = canvas.toDataURL('image/png');
    setCanvasDataUrl(dataUrl);
    
    console.log('Canvas is ready!', canvas);
  };
  
  // Download canvas as image
  const downloadImage = () => {
    if (!canvasDataUrl) return;
    
    const link = document.createElement('a');
    link.download = 'letter-preview.png';
    link.href = canvasDataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <div>
      <h2>Auto Canvas Example</h2>
      
      {/* AutoCanvas with PagejsViewer as child */}
      <AutoCanvas 
        onCanvasReady={handleCanvasReady}
        autoConvert={true}
        showOriginal={true}
        ref={autoCanvasRef}
        containerStyle={{ margin: '20px 0' }}
      >
        <PagejsViewer letter={letter} layout={layout} />
      </AutoCanvas>
      
      {/* Download button */}
      {canvasDataUrl && (
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <button 
            onClick={downloadImage}
            style={{ padding: '10px 20px' }}
          >
            Download as PNG
          </button>
        </div>
      )}
      
      {/* Preview of the generated image */}
      {canvasDataUrl && (
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <h3>Generated Image Preview:</h3>
          <img 
            src={canvasDataUrl} 
            alt="Canvas Preview" 
            style={{ 
              maxWidth: '100%',
              border: '1px solid #ccc'
            }} 
          />
        </div>
      )}
    </div>
  );
};

export default AutoCanvasExample;
