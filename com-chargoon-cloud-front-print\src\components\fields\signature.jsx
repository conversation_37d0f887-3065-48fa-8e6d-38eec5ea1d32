import React from 'react';
import { createUseStyles } from 'react-jss';
import PropTypes from 'prop-types';
import SignerName from './signerName';
import config from './config';
import { isNumeric } from "./common";

const useStyles = createUseStyles({
  root: (props) => ({
    position: 'absolute',
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'wrap-reverse',
    right: props?.signature?.right || isNumeric(props?.signature?.right) ? `${props?.signature?.right}cm` : "unset",
    left: props?.signature?.left || isNumeric(props?.signature?.left) ? `${props?.signature?.left}cm` : "unset",
    top: (props.signature.top ? `${props.signature.top}cm` : null),
    bottom: (props.signature.bottom ? `${props.signature.bottom}cm` : null),
  }),
  signature: (props) => ({
    direction: 'rtl',
    padding: '5px',
    textAlign: props.signature?.align || 'start',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  }),
  signatureImg: (props) => ({
    // objectFit:'contain',
    maxWidth: `${props.signature?.maxSize || 100}px`,
    maxHeight: `${props.signature?.maxSize || 100}px`,
    zIndex: 0,
  }),
  signatureBlank: () => ({
    height: `80px`,
  }),
  signatureName: (props) => ({
    position: 'absolute',
    zIndex: props.signature?.signerNameOnTop ? 1 : -1,
    marginTop: (props.signature?.signerNameDirection === 'up' || props.signature?.signerNameDirection === 'down')
        ? (props.signature?.signerNameDirection === 'up' ? `-${props.signature?.signerNameOffset}cm` : `${props.signature?.signerNameOffset}cm`)
        : '0cm',
    marginRight: (props.signature?.signerNameDirection === 'right' || props.signature?.signerNameDirection === 'left')
        ? (props.signature?.signerNameDirection === 'right' ? `-${props.signature?.signerNameOffset}cm` : `${props.signature?.signerNameOffset}cm`)
        : '0cm',
  }),
  bodyHeight: {
    lineHeight: `${config.PRINT_DEFAULT_TOKEN_LINE_HEIGHT}em`,
  }
});

const Signature = (props) => {
  const {letter, signature} = props;

  if (!signature?.signerNameOnTop) {
    signature.signerNameOnTop = false;
  }
  if (!signature?.signerNameOffset) {
    signature.signerNameOffset = 1.5; //cm
  }
  if (!signature?.signerNameDirection) {
    signature.signerNameDirection = 'up';
  }

  const classes = useStyles({ signature });

  return (
    <div className={classes.bodyHeight}>
        <div className={classes.root}>
          {letter.signedBy?.length > 0 && letter.signedBy.map((sign, i) => (
              <div key={i} className={classes.signature}>
                {sign.signature ? (<img alt="signature" src={`data:image/png;base64,${sign.signature}`} className={classes.signatureImg}/>) : (<span className={classes.signatureBlank}></span>)}
                {(signature.signerName || signature.isSignerName) && (<span className={classes.signatureName}><SignerName signature={signature} letter={letter} signInfo={sign}/></span>)}
            </div>
          ))}
      </div>
    </div>
  );
};

Signature.propTypes = {
  signature: PropTypes.object,
  letter: PropTypes.object.isRequired,
};

export default Signature;
