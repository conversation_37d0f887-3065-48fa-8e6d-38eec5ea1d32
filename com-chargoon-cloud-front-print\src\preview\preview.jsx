import React from "react";
import { Previewer } from "pagedjs";
import { PagejsViewer } from "../pagedjs";

const Preview = ({ letter, layout }) => {

  const previewer = new Previewer();

  // Native canvas conversion functions (no external libraries)
  const convertToCanvas = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");
      const canvasContainer = document.getElementById("canvas-container");
      canvasContainer.innerHTML = ""; // Clear previous canvases

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        canvas.style.margin = "10px";
        canvas.style.border = "1px solid #ccc";
        canvasContainer.appendChild(canvas);
      }
    } catch (error) {
      console.error("Canvas conversion error:", error);
    }
  };

  const convertToImage = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");
      const imageContainer = document.getElementById("image-container");
      imageContainer.innerHTML = ""; // Clear previous images

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        const img = document.createElement("img");
        img.src = canvas.toDataURL("image/png");
        img.style.margin = "10px";
        img.style.border = "1px solid #ccc";
        img.style.maxWidth = "100%";
        imageContainer.appendChild(img);
      }
    } catch (error) {
      console.error("Image conversion error:", error);
    }
  };

  const downloadAsImage = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        const dataURL = canvas.toDataURL("image/png");

        // Create download link
        const link = document.createElement("a");
        link.download = `page-${i + 1}.png`;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Download error:", error);
    }
  };

  // Enhanced function to convert DOM element to canvas with proper fonts and styles
  const elementToCanvas = async (element) => {
    return new Promise(async (resolve, reject) => {
      try {
        const rect = element.getBoundingClientRect();
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Set high DPI for better quality
        const scale = window.devicePixelRatio || 1;
        canvas.width = rect.width * scale;
        canvas.height = rect.height * scale;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
        ctx.scale(scale, scale);

        // Create SVG with foreign object containing the HTML
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("width", rect.width);
        svg.setAttribute("height", rect.height);
        svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");

        // Add all CSS styles to SVG
        const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
        const style = document.createElementNS("http://www.w3.org/2000/svg", "style");

        // Get all styles from the document
        let allStyles = await getAllDocumentStyles();
        style.textContent = allStyles;
        defs.appendChild(style);
        svg.appendChild(defs);

        const foreignObject = document.createElementNS("http://www.w3.org/2000/svg", "foreignObject");
        foreignObject.setAttribute("width", "100%");
        foreignObject.setAttribute("height", "100%");

        // Clone the element and apply inline styles
        const clonedElement = element.cloneNode(true);
        await applyInlineStyles(clonedElement, element);

        foreignObject.appendChild(clonedElement);
        svg.appendChild(foreignObject);

        // Convert SVG to data URL
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
        const svgUrl = URL.createObjectURL(svgBlob);

        // Draw SVG to canvas
        const img = new Image();
        img.onload = () => {
          // Fill white background
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, rect.width, rect.height);
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(svgUrl);
          resolve(canvas);
        };
        img.onerror = (error) => {
          URL.revokeObjectURL(svgUrl);
          reject(error);
        };
        img.src = svgUrl;

      } catch (error) {
        reject(error);
      }
    });
  };

  // Get all document styles including fonts
  const getAllDocumentStyles = async () => {
    let styles = '';

    try {
      // Get all stylesheets
      for (let i = 0; i < document.styleSheets.length; i++) {
        try {
          const styleSheet = document.styleSheets[i];
          if (styleSheet.cssRules || styleSheet.rules) {
            const rules = styleSheet.cssRules || styleSheet.rules;
            for (let j = 0; j < rules.length; j++) {
              const rule = rules[j];
              if (rule.cssText) {
                styles += rule.cssText + '\n';
              }
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
          console.warn('Cannot access stylesheet:', e);
        }
      }

      // Get inline styles
      const styleElements = document.querySelectorAll('style');
      styleElements.forEach(styleEl => {
        styles += styleEl.textContent + '\n';
      });

      // Add font loading styles
      styles += `
        * {
          font-display: block !important;
        }
      `;

    } catch (error) {
      console.warn('Error getting styles:', error);
    }

    return styles;
  };

  // Apply inline styles to preserve appearance
  const applyInlineStyles = async (clonedElement, originalElement) => {
    try {
      const computedStyle = window.getComputedStyle(originalElement);

      // Apply all computed styles as inline styles
      let inlineStyle = '';
      for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle[i];
        const value = computedStyle.getPropertyValue(property);
        if (value && value !== 'initial' && value !== 'inherit') {
          inlineStyle += `${property}: ${value} !important; `;
        }
      }

      clonedElement.style.cssText = inlineStyle;

      // Process children recursively
      const originalChildren = originalElement.children;
      const clonedChildren = clonedElement.children;

      for (let i = 0; i < originalChildren.length && i < clonedChildren.length; i++) {
        await applyInlineStyles(clonedChildren[i], originalChildren[i]);
      }

    } catch (error) {
      console.warn('Error applying inline styles:', error);
    }
  };



  return (
    <>
      <PagejsViewer {...{ letter, layout, previewer }} />

      {/* Add conversion buttons */}
      <div style={{ margin: "20px 0" }}>
        <button onClick={convertToCanvas}>Convert to Canvas</button>
        <button onClick={convertToImage}>Convert to Image</button>
        <button onClick={downloadAsImage}>Download as Image</button>
      </div>

      {/* Canvas container */}
      <div id="canvas-container"></div>

      {/* Image container */}
      <div id="image-container"></div>
    </>
  );
};

export default Preview;
