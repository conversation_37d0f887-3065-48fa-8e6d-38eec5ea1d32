import React, { useEffect } from "react";
import { Previewer } from "pagedjs";
import { renderToStaticMarkup } from "react-dom/server";
import { usePreview } from "./preview-provider";
import { toCanvas } from "html-to-image";
// import html2canvas from "html2canvas";
import Watermark from "../components/fields/watermark";
import { PagejsViewer } from "../pagedjs";
import { c2p } from "../components/utils";

const Preview = ({ letter, layout }) => {
  const {
    images,
    setImages,
    currentPage,
    setCurrentPage,
    canvasRef,
    handleChangeLoading,
    loadingLayout,
    handleSetError,
  } = usePreview();

  const previewer = new Previewer();

  // Native canvas conversion functions (no external libraries)
  const convertToCanvas = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");
      const canvasContainer = document.getElementById("canvas-container");
      canvasContainer.innerHTML = ""; // Clear previous canvases

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        canvas.style.margin = "10px";
        canvas.style.border = "1px solid #ccc";
        canvasContainer.appendChild(canvas);
      }
    } catch (error) {
      console.error("Canvas conversion error:", error);
    }
  };

  const convertToImage = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");
      const imageContainer = document.getElementById("image-container");
      imageContainer.innerHTML = ""; // Clear previous images

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        const img = document.createElement("img");
        img.src = canvas.toDataURL("image/png");
        img.style.margin = "10px";
        img.style.border = "1px solid #ccc";
        img.style.maxWidth = "100%";
        imageContainer.appendChild(img);
      }
    } catch (error) {
      console.error("Image conversion error:", error);
    }
  };

  const downloadAsImage = async () => {
    try {
      const letterContainer = document.getElementById("letter-preview");
      if (!letterContainer) return;

      const pages = letterContainer.querySelectorAll(".pagedjs_page");

      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = await elementToCanvas(page);
        const dataURL = canvas.toDataURL("image/png");

        // Create download link
        const link = document.createElement("a");
        link.download = `page-${i + 1}.png`;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Download error:", error);
    }
  };

  // Native function to convert DOM element to canvas (without external libraries)
  const elementToCanvas = async (element) => {
    return new Promise((resolve, reject) => {
      try {
        const rect = element.getBoundingClientRect();
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Set canvas size to match element
        canvas.width = rect.width;
        canvas.height = rect.height;

        // Create SVG with foreign object containing the HTML
        const svg = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "svg"
        );
        svg.setAttribute("width", rect.width);
        svg.setAttribute("height", rect.height);

        const foreignObject = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "foreignObject"
        );
        foreignObject.setAttribute("width", "100%");
        foreignObject.setAttribute("height", "100%");

        // Clone the element to avoid modifying the original
        const clonedElement = element.cloneNode(true);

        // Get computed styles and apply them inline
        const computedStyle = window.getComputedStyle(element);
        clonedElement.style.cssText = computedStyle.cssText;

        // Apply styles to all child elements
        const applyStylesToChildren = (original, clone) => {
          const originalChildren = original.children;
          const cloneChildren = clone.children;

          for (let i = 0; i < originalChildren.length; i++) {
            if (originalChildren[i] && cloneChildren[i]) {
              const childComputedStyle = window.getComputedStyle(
                originalChildren[i]
              );
              cloneChildren[i].style.cssText = childComputedStyle.cssText;
              applyStylesToChildren(originalChildren[i], cloneChildren[i]);
            }
          }
        };

        applyStylesToChildren(element, clonedElement);

        foreignObject.appendChild(clonedElement);
        svg.appendChild(foreignObject);

        // Convert SVG to data URL
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], {
          type: "image/svg+xml;charset=utf-8",
        });
        const svgUrl = URL.createObjectURL(svgBlob);

        // Draw SVG to canvas
        const img = new Image();
        img.onload = () => {
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(svgUrl);
          resolve(canvas);
        };
        img.onerror = reject;
        img.src = svgUrl;
      } catch (error) {
        reject(error);
      }
    });
  };

  const runPagedPreview = (letterVal, layoutVal) => {
    try {
      const letterContainer = document.getElementById("letter-preview");

      previewer.on("rendered", async (flow) => {
        const lastChild = letterContainer?.lastElementChild;
        if (!lastChild) return;
        const pages = lastChild?.querySelectorAll(".pagedjs_page");
        if (!pages || pages.length === 0) {
          handleChangeLoading(false);
          handleSetError("No pages generated.");
          return;
        }
        Array.from(pages).forEach((page, index) => {
          page.style.position = "relative";
          const hasWatermark = page.querySelector(".watermark");
          if (!hasWatermark) {
            const wm = renderToStaticMarkup(<Watermark />);
            page.innerHTML += wm;
          }
        });
        const canvasPromises = Array.from(pages).map(async (page) => {
          // const canvas = await html2canvas(page, {scale:1});
          const canvas = await toCanvas(page, {
            skipFonts: false,
            style: {
              direction: "rtl",
            },
          });
          return canvas.toDataURL();
        });
        const images = await Promise.all(canvasPromises);
        setImages(images);
        setCurrentPage((prev) =>
          Math.max(0, prev < flow.total ? prev : flow.total - 1)
        );
        setTimeout(() => {
          const letterBody = document.getElementById("letter-body");
          if (letterBody) letterBody.innerHTML = "";
          // if (letterContainer) letterContainer.innerHTML = "";
          handleChangeLoading(false);
          handleSetError(undefined);
        }, 500);
      });
    } catch (error) {
      console.error(error);
      handleSetError(error);
      handleChangeLoading(false);
    }
  };

  useEffect(() => {
    runPagedPreview(letter, layout);
  }, [layout, letter]);

  // Draw canvas image
  useEffect(() => {
    try {
      if (!images.length || currentPage >= images.length) return;
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext("2d");
      const image = new Image();

      image.onload = () => {
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(image, 0, 0);
      };

      image.src = images[currentPage];
      handleSetError(undefined);
    } catch (error) {
      console.error(error);
      handleSetError(error);
    }
  }, [images, currentPage]);

  return (
    <>
      <PagejsViewer {...{ letter, layout, previewer }} />

      {/* Add conversion buttons */}
      <div style={{ margin: "20px 0" }}>
        <button onClick={convertToCanvas}>Convert to Canvas</button>
        <button onClick={convertToImage}>Convert to Image</button>
        <button onClick={downloadAsImage}>Download as Image</button>
      </div>

      {/* Canvas container */}
      <div id="canvas-container"></div>

      {/* Image container */}
      <div id="image-container"></div>
    </>
  );
};

export default Preview;
