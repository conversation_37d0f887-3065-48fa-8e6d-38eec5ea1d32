import React, { useRef, useEffect, useState } from "react";

const AutoCanvas = ({ 
  children, 
  onCanvasReady = null,
  autoConvert = true,
  showOriginal = false,
  canvasStyle = {},
  containerStyle = {}
}) => {
  const elementRef = useRef(null);
  const [canvas, setCanvas] = useState(null);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState(null);

  // Enhanced function to convert DOM element to canvas with proper fonts and styles
  const elementToCanvas = async (element) => {
    return new Promise(async (resolve, reject) => {
      try {
        const rect = element.getBoundingClientRect();
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Set high DPI for better quality
        const scale = window.devicePixelRatio || 1;
        canvas.width = rect.width * scale;
        canvas.height = rect.height * scale;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
        ctx.scale(scale, scale);

        // Create SVG with foreign object containing the HTML
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("width", rect.width);
        svg.setAttribute("height", rect.height);
        svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");

        // Add all CSS styles to SVG
        const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
        const style = document.createElementNS("http://www.w3.org/2000/svg", "style");
        
        // Get all styles from the document
        let allStyles = await getAllDocumentStyles();
        style.textContent = allStyles;
        defs.appendChild(style);
        svg.appendChild(defs);

        const foreignObject = document.createElementNS("http://www.w3.org/2000/svg", "foreignObject");
        foreignObject.setAttribute("width", "100%");
        foreignObject.setAttribute("height", "100%");

        // Clone the element and apply inline styles
        const clonedElement = element.cloneNode(true);
        await applyInlineStyles(clonedElement, element);

        foreignObject.appendChild(clonedElement);
        svg.appendChild(foreignObject);

        // Convert SVG to data URL
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
        const svgUrl = URL.createObjectURL(svgBlob);

        // Draw SVG to canvas
        const img = new Image();
        img.onload = () => {
          // Fill white background
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, rect.width, rect.height);
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(svgUrl);
          resolve(canvas);
        };
        img.onerror = (error) => {
          URL.revokeObjectURL(svgUrl);
          reject(error);
        };
        img.src = svgUrl;

      } catch (error) {
        reject(error);
      }
    });
  };

  // Get all document styles including fonts
  const getAllDocumentStyles = async () => {
    let styles = '';
    
    try {
      // Get all stylesheets
      for (let i = 0; i < document.styleSheets.length; i++) {
        try {
          const styleSheet = document.styleSheets[i];
          if (styleSheet.cssRules) {
            const rules = styleSheet.cssRules;
            for (let j = 0; j < rules.length; j++) {
              const rule = rules[j];
              if (rule.cssText) {
                styles += rule.cssText + '\n';
              }
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
          console.warn('Cannot access stylesheet:', e);
        }
      }
      
      // Get inline styles
      const styleElements = document.querySelectorAll('style');
      styleElements.forEach(styleEl => {
        styles += styleEl.textContent + '\n';
      });
      
      // Add font loading styles
      styles += `
        * {
          font-display: block !important;
        }
      `;
      
    } catch (error) {
      console.warn('Error getting styles:', error);
    }
    
    return styles;
  };

  // Apply inline styles to preserve appearance
  const applyInlineStyles = async (clonedElement, originalElement) => {
    try {
      const computedStyle = window.getComputedStyle(originalElement);
      
      // Apply all computed styles as inline styles
      let inlineStyle = '';
      for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle[i];
        const value = computedStyle.getPropertyValue(property);
        if (value && value !== 'initial' && value !== 'inherit') {
          inlineStyle += `${property}: ${value} !important; `;
        }
      }
      
      clonedElement.style.cssText = inlineStyle;
      
      // Process children recursively
      const originalChildren = originalElement.children;
      const clonedChildren = clonedElement.children;
      
      for (let i = 0; i < originalChildren.length && i < clonedChildren.length; i++) {
        await applyInlineStyles(clonedChildren[i], originalChildren[i]);
      }
      
    } catch (error) {
      console.warn('Error applying inline styles:', error);
    }
  };

  // Convert element to canvas
  const convertToCanvas = async () => {
    if (!elementRef.current) return;
    
    setIsConverting(true);
    setError(null);
    
    try {
      const canvasElement = await elementToCanvas(elementRef.current);
      setCanvas(canvasElement);
      
      // Call callback if provided
      if (onCanvasReady) {
        onCanvasReady(canvasElement);
      }
    } catch (err) {
      console.error('Canvas conversion error:', err);
      setError(err.message);
    } finally {
      setIsConverting(false);
    }
  };

  // Auto convert when element is ready
  useEffect(() => {
    if (autoConvert && elementRef.current) {
      // Wait a bit for styles to load
      const timer = setTimeout(() => {
        convertToCanvas();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [children, autoConvert]);

  // Manual convert function (exposed via ref)
  const manualConvert = () => {
    convertToCanvas();
  };

  return (
    <div style={{ ...containerStyle }}>
      {/* Original element (hidden if showOriginal is false) */}
      <div 
        ref={elementRef}
        style={{ 
          display: showOriginal ? 'block' : 'none',
          position: showOriginal ? 'relative' : 'absolute',
          left: showOriginal ? 'auto' : '-9999px',
          top: showOriginal ? 'auto' : '-9999px',
          visibility: showOriginal ? 'visible' : 'hidden'
        }}
      >
        {children}
      </div>

      {/* Canvas output */}
      {canvas && (
        <div style={{ textAlign: 'center', margin: '10px 0' }}>
          <canvas
            ref={(ref) => {
              if (ref && canvas) {
                ref.width = canvas.width;
                ref.height = canvas.height;
                ref.style.width = canvas.style.width;
                ref.style.height = canvas.style.height;
                const ctx = ref.getContext('2d');
                ctx.drawImage(canvas, 0, 0);
              }
            }}
            style={{
              maxWidth: '100%',
              border: '1px solid #ccc',
              ...canvasStyle
            }}
          />
        </div>
      )}

      {/* Loading indicator */}
      {isConverting && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          Converting to canvas...
        </div>
      )}

      {/* Error display */}
      {error && (
        <div style={{ 
          color: 'red', 
          textAlign: 'center', 
          padding: '10px',
          border: '1px solid red',
          borderRadius: '4px',
          margin: '10px 0'
        }}>
          Error: {error}
        </div>
      )}

      {/* Manual convert button (if auto convert is disabled) */}
      {!autoConvert && (
        <div style={{ textAlign: 'center', margin: '10px 0' }}>
          <button 
            onClick={manualConvert}
            disabled={isConverting}
            style={{ padding: '10px 20px' }}
          >
            {isConverting ? 'Converting...' : 'Convert to Canvas'}
          </button>
        </div>
      )}
    </div>
  );
};

export default AutoCanvas;
