import { useRef, useEffect, useState } from "react";

const AutoCanvas = ({ children }) => {
  const elementRef = useRef(null);
  const [canvas, setCanvas] = useState(null);

  // Simple function to convert DOM element to canvas
  const elementToCanvas = async (element) => {
    return new Promise((resolve, reject) => {
      try {
        const rect = element.getBoundingClientRect();
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        canvas.width = rect.width;
        canvas.height = rect.height;

        // Create SVG with foreign object
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("width", rect.width);
        svg.setAttribute("height", rect.height);

        const foreignObject = document.createElementNS("http://www.w3.org/2000/svg", "foreignObject");
        foreignObject.setAttribute("width", "100%");
        foreignObject.setAttribute("height", "100%");

        // Clone the element
        const clonedElement = element.cloneNode(true);
        foreignObject.appendChild(clonedElement);
        svg.appendChild(foreignObject);

        // Convert to canvas
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
        const svgUrl = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = () => {
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, rect.width, rect.height);
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(svgUrl);
          resolve(canvas);
        };
        img.onerror = reject;
        img.src = svgUrl;

      } catch (error) {
        reject(error);
      }
    });
  };

  // Convert to canvas when ready
  const convertToCanvas = async () => {
    if (!elementRef.current) return;

    try {
      const canvasElement = await elementToCanvas(elementRef.current);
      setCanvas(canvasElement);
    } catch (error) {
      console.error('Canvas conversion error:', error);
    }
  };



  // Auto convert when element is ready
  useEffect(() => {
    if (elementRef.current) {
      const timer = setTimeout(() => {
        convertToCanvas();
      }, 2000); // Wait 2 seconds for DOM to be ready

      return () => clearTimeout(timer);
    }
  }, [children]);

  return (
    <div>
      {/* Original element (hidden) */}
      <div
        ref={elementRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '-9999px',
          visibility: 'hidden'
        }}
      >
        {children}
      </div>

      {/* Canvas output */}
      {canvas && (
        <div style={{ textAlign: 'center' }}>
          <canvas
            ref={(ref) => {
              if (ref && canvas) {
                ref.width = canvas.width;
                ref.height = canvas.height;
                const ctx = ref.getContext('2d');
                ctx.drawImage(canvas, 0, 0);
              }
            }}
            style={{ maxWidth: '100%' }}
          />
        </div>
      )}
    </div>
  );
};

export default AutoCanvas;
