import { useRef, useEffect, useState } from "react";

const AutoCanvas = ({ children }) => {
  const elementRef = useRef(null);
  const [canvas, setCanvas] = useState(null);
  const [isConverting, setIsConverting] = useState(false);

  // Simple function to convert DOM element to canvas
  const elementToCanvas = async (element) => {
    return new Promise((resolve, reject) => {
      try {
        const rect = element.getBoundingClientRect();
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        canvas.width = rect.width;
        canvas.height = rect.height;

        // Create SVG with foreign object
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("width", rect.width);
        svg.setAttribute("height", rect.height);

        const foreignObject = document.createElementNS("http://www.w3.org/2000/svg", "foreignObject");
        foreignObject.setAttribute("width", "100%");
        foreignObject.setAttribute("height", "100%");

        // Clone the element
        const clonedElement = element.cloneNode(true);
        foreignObject.appendChild(clonedElement);
        svg.appendChild(foreignObject);

        // Convert to canvas
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
        const svgUrl = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = () => {
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, rect.width, rect.height);
          ctx.drawImage(img, 0, 0);
          URL.revokeObjectURL(svgUrl);
          resolve(canvas);
        };
        img.onerror = reject;
        img.src = svgUrl;

      } catch (error) {
        reject(error);
      }
    });
  };

  // Convert to canvas when ready
  const convertToCanvas = async () => {
    if (!elementRef.current) return;

    setIsConverting(true);

    try {
      // Temporarily make element visible for measurement
      const element = elementRef.current;
      const originalStyle = {
        position: element.style.position,
        left: element.style.left,
        top: element.style.top,
        visibility: element.style.visibility
      };

      // Make visible temporarily
      element.style.position = 'relative';
      element.style.left = 'auto';
      element.style.top = 'auto';
      element.style.visibility = 'visible';

      // Wait a moment for layout
      await new Promise(resolve => setTimeout(resolve, 100));

      const canvasElement = await elementToCanvas(element);
      setCanvas(canvasElement);

      // Hide element again
      element.style.position = originalStyle.position;
      element.style.left = originalStyle.left;
      element.style.top = originalStyle.top;
      element.style.visibility = originalStyle.visibility;

    } catch (error) {
      console.error('Canvas conversion error:', error);
    } finally {
      setIsConverting(false);
    }
  };



  // Auto convert when element is ready
  useEffect(() => {
    if (elementRef.current) {
      const timer = setTimeout(() => {
        console.log('Starting canvas conversion...');
        convertToCanvas();
      }, 3000); // Wait 3 seconds for PagedJS to finish

      return () => clearTimeout(timer);
    }
  }, [children]);

  return (
    <div>
      {/* Original element (hidden unless converting) */}
      <div
        ref={elementRef}
        style={{
          position: canvas ? 'absolute' : 'relative',
          left: canvas ? '-9999px' : 'auto',
          top: canvas ? '-9999px' : 'auto',
          visibility: canvas ? 'hidden' : 'visible'
        }}
      >
        {children}
      </div>

      {/* Loading indicator */}
      {isConverting && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          Converting to canvas...
        </div>
      )}

      {/* Canvas output */}
      {canvas && (
        <div style={{ textAlign: 'center' }}>
          <canvas
            ref={(ref) => {
              if (ref && canvas) {
                try {
                  ref.width = canvas.width;
                  ref.height = canvas.height;
                  const ctx = ref.getContext('2d');
                  ctx.drawImage(canvas, 0, 0);
                  console.log('Canvas drawn successfully');
                } catch (error) {
                  console.error('Error drawing canvas:', error);
                }
              }
            }}
            style={{ maxWidth: '100%', border: '1px solid #ccc' }}
          />
        </div>
      )}
    </div>
  );
};

export default AutoCanvas;
