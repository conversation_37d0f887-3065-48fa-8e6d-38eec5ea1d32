const puppeteer = require('puppeteer');
const generateFilePath = require('../../letter/index');
const logger = require('../../utils/logger');
const defaultLayout = require('./defaultLayout');
const path = require('node:path');
const jalaali = require('jalaali-js');
const tokenFinder = require('../../letter/token.finder');
const { c2p } = require('../../utils/convertCentimeterToPixel');

module.exports = async (req, res) => {
  const headless = false;
  let browser, page;

  try {
    const letter = req?.body?.letter || {};
    let layout = req?.body?.layout || {};

    // Handle backward compatibility: if content is missing, inject a default empty block
    if (!letter?.content) {
      letter.content = {
        blocks: [
          {
            key: 'beu13',
            text: '',
            type: 'unstyled',
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {},
          },
        ],
        entityMap: {},
      };
    }

    // Enforce layout size limit
    const layoutSize = Buffer.byteLength(JSON.stringify(layout));
    const maxLayoutSize = Number(process.env.MAX_LAYOUT_SIZE) || 1024 * 1024;
    if (layoutSize > maxLayoutSize) {
      throw new Error(`The layout size ${layoutSize}b exceeds the allowed maximum of ${maxLayoutSize}b`);
    }

    // Use default layout if not provided
    if (!layout || Object.keys(layout).length === 0) {
      layout = JSON.parse(JSON.stringify(defaultLayout.A4));
    } else {
      layout = JSON.parse(JSON.stringify(layout));
    }

    // Localize letter date based on layout token
    const dateToken = tokenFinder.findTokenInSection(layout, 'date');
    if (dateToken) {
      const date = new Date(letter.date);
      if (dateToken.date?.lang === 'en') {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        letter.date = `${year}-${month}-${day}`;
      } else {
        const d = jalaali.toJalaali(date);
        letter.date = `${d.jy}/${d.jm}/${d.jd}`;
      }
    }

    // Launch Puppeteer
    browser = await puppeteer.launch({
      headless,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage', '--disable-gpu'],
    });

    [page] = await browser.pages();

    const dpi = 96;
    const width = c2p(layout.paper.width, dpi);
    const height = c2p(layout.paper.height, dpi);

    const filePath = generateFilePath({ letter, layout });

    // await page.setViewport({
    //   width: parseInt(width),
    //   height: parseInt(height),
    // });

    // await writeFileSync('file.html', filePath);

    await page.goto(filePath, { waitUntil: 'networkidle0' });

    // Ensure fonts are fully loaded
    await page.evaluateHandle('document.fonts.ready');

    // Remove all margins and padding from the page
    await page.addStyleTag({
      content: `
        @page {
          size: ${layout.paper.width}cm ${layout.paper.height}cm !important;
        }
      `,
    });

    // Wait for paged.js to finish rendering
    await page.evaluate(() => window.PagedPolyfill?.ready);

    await page.emulateMediaType('print');

    const pdfBuffer = await page.pdf({
      width: `${layout.paper.width}cm`,
      height: `${layout.paper.height}cm`,
      printBackground: true,
      displayHeaderFooter: false,
      margin: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      },
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${Date.now()}.pdf`);
    return res.status(200).send(pdfBuffer);
  } catch (err) {
    logger.error(`puppeteer error: ${err.message}`, {
      message: err.message,
      stack: err.stack,
    });

    return res.status(400).send({
      status: false,
      message: err.message,
    });
  } finally {
    if (headless) {
      try {
        if (page) await page.close();
        if (browser) await browser.close();
      } catch (closeErr) {
        logger.warn('Error closing Puppeteer resources', closeErr);
      }
    }
  }
};
