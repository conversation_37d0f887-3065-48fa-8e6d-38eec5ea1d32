import React, { useState } from "react";
import Body from "../components/body";
import { useEffect } from "react";
import setLayoutProperties from "../components/utils/setLayoutProperties";
import { Previewer } from "pagedjs";
import { CreateCssBlob } from "../components/utils";
import { JssProvider } from "react-jss";

const cssUrl = CreateCssBlob();

export const PagejsViewer = ({
  letter,
  layout,
  previewer = new Previewer(),
}) => {
  const initial = async () => {
    try {
      const source = document.getElementById("letter-body");
      const letterContainer = document.getElementById("letter-preview");

      if (letterContainer) {
        letterContainer.style.fontFamily = layout.paper.fontFamily;
      }

      setLayoutProperties(layout);
      await previewer.preview(source, [cssUrl], letterContainer);

      // remove letter-body
      source.remove();

      // previewer.on("rendered", async (flow) => {
      //   const letterContainer = document.getElementById("letter-preview");
      //   const lastChild = letterContainer?.lastElementChild;
      //   if (!lastChild) return;
      //   const pages = lastChild?.querySelectorAll(".pagedjs_page");
      //   if (!pages || pages.length === 0) {
      //     handleSetError("No pages generated.");
      //     return;
      //   }
      //   Array.from(pages).forEach((page, index) => {
      //     const pageNumber = page.querySelector("#pageNumber");
      //     if (pageNumber) {
      //       pageNumber.textContent = `${flow.total} / ${index + 1}`;
      //     }
      //     page.style.position = "relative";
      //   });
      // });
    } catch (error) {
      console.error("PagedJS preview error:", error);
    }
  };

  useEffect(() => {
    initial();
  }, [letter, layout]);

  return (
    <>
      <JssProvider>
        <div id="letter-body">
          <Body letter={letter} layout={layout} />
        </div>
        <div id="letter-preview"></div>
      </JssProvider>
    </>
  );
};
