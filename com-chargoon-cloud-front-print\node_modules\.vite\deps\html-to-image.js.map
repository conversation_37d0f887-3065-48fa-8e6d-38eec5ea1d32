{"version": 3, "sources": ["../../html-to-image/src/util.ts", "../../html-to-image/src/clone-pseudos.ts", "../../html-to-image/src/mimes.ts", "../../html-to-image/src/dataurl.ts", "../../html-to-image/src/clone-node.ts", "../../html-to-image/src/embed-resources.ts", "../../html-to-image/src/embed-images.ts", "../../html-to-image/src/apply-style.ts", "../../html-to-image/src/embed-webfonts.ts", "../../html-to-image/src/index.ts"], "sourcesContent": ["import type { Options } from './types'\n\nexport function resolveUrl(url: string, baseUrl: string | null): string {\n  // url is absolute already\n  if (url.match(/^[a-z]+:\\/\\//i)) {\n    return url\n  }\n\n  // url is absolute already, without protocol\n  if (url.match(/^\\/\\//)) {\n    return window.location.protocol + url\n  }\n\n  // dataURI, mailto:, tel:, etc.\n  if (url.match(/^[a-z]+:/i)) {\n    return url\n  }\n\n  const doc = document.implementation.createHTMLDocument()\n  const base = doc.createElement('base')\n  const a = doc.createElement('a')\n\n  doc.head.appendChild(base)\n  doc.body.appendChild(a)\n\n  if (baseUrl) {\n    base.href = baseUrl\n  }\n\n  a.href = url\n\n  return a.href\n}\n\nexport const uuid = (() => {\n  // generate uuid for className of pseudo elements.\n  // We should not use GUIDs, otherwise pseudo elements sometimes cannot be captured.\n  let counter = 0\n\n  // ref: http://stackoverflow.com/a/6248722/2519373\n  const random = () =>\n    // eslint-disable-next-line no-bitwise\n    `0000${((Math.random() * 36 ** 4) << 0).toString(36)}`.slice(-4)\n\n  return () => {\n    counter += 1\n    return `u${random()}${counter}`\n  }\n})()\n\nexport function delay<T>(ms: number) {\n  return (args: T) =>\n    new Promise<T>((resolve) => {\n      setTimeout(() => resolve(args), ms)\n    })\n}\n\nexport function toArray<T>(arrayLike: any): T[] {\n  const arr: T[] = []\n\n  for (let i = 0, l = arrayLike.length; i < l; i++) {\n    arr.push(arrayLike[i])\n  }\n\n  return arr\n}\n\nlet styleProps: string[] | null = null\nexport function getStyleProperties(options: Options = {}): string[] {\n  if (styleProps) {\n    return styleProps\n  }\n\n  if (options.includeStyleProperties) {\n    styleProps = options.includeStyleProperties\n    return styleProps\n  }\n\n  styleProps = toArray(window.getComputedStyle(document.documentElement))\n\n  return styleProps\n}\n\nfunction px(node: HTMLElement, styleProperty: string) {\n  const win = node.ownerDocument.defaultView || window\n  const val = win.getComputedStyle(node).getPropertyValue(styleProperty)\n  return val ? parseFloat(val.replace('px', '')) : 0\n}\n\nfunction getNodeWidth(node: HTMLElement) {\n  const leftBorder = px(node, 'border-left-width')\n  const rightBorder = px(node, 'border-right-width')\n  return node.clientWidth + leftBorder + rightBorder\n}\n\nfunction getNodeHeight(node: HTMLElement) {\n  const topBorder = px(node, 'border-top-width')\n  const bottomBorder = px(node, 'border-bottom-width')\n  return node.clientHeight + topBorder + bottomBorder\n}\n\nexport function getImageSize(targetNode: HTMLElement, options: Options = {}) {\n  const width = options.width || getNodeWidth(targetNode)\n  const height = options.height || getNodeHeight(targetNode)\n\n  return { width, height }\n}\n\nexport function getPixelRatio() {\n  let ratio\n\n  let FINAL_PROCESS\n  try {\n    FINAL_PROCESS = process\n  } catch (e) {\n    // pass\n  }\n\n  const val =\n    FINAL_PROCESS && FINAL_PROCESS.env\n      ? FINAL_PROCESS.env.devicePixelRatio\n      : null\n  if (val) {\n    ratio = parseInt(val, 10)\n    if (Number.isNaN(ratio)) {\n      ratio = 1\n    }\n  }\n  return ratio || window.devicePixelRatio || 1\n}\n\n// @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/canvas#maximum_canvas_size\nconst canvasDimensionLimit = 16384\n\nexport function checkCanvasDimensions(canvas: HTMLCanvasElement) {\n  if (\n    canvas.width > canvasDimensionLimit ||\n    canvas.height > canvasDimensionLimit\n  ) {\n    if (\n      canvas.width > canvasDimensionLimit &&\n      canvas.height > canvasDimensionLimit\n    ) {\n      if (canvas.width > canvas.height) {\n        canvas.height *= canvasDimensionLimit / canvas.width\n        canvas.width = canvasDimensionLimit\n      } else {\n        canvas.width *= canvasDimensionLimit / canvas.height\n        canvas.height = canvasDimensionLimit\n      }\n    } else if (canvas.width > canvasDimensionLimit) {\n      canvas.height *= canvasDimensionLimit / canvas.width\n      canvas.width = canvasDimensionLimit\n    } else {\n      canvas.width *= canvasDimensionLimit / canvas.height\n      canvas.height = canvasDimensionLimit\n    }\n  }\n}\n\nexport function canvasToBlob(\n  canvas: HTMLCanvasElement,\n  options: Options = {},\n): Promise<Blob | null> {\n  if (canvas.toBlob) {\n    return new Promise((resolve) => {\n      canvas.toBlob(\n        resolve,\n        options.type ? options.type : 'image/png',\n        options.quality ? options.quality : 1,\n      )\n    })\n  }\n\n  return new Promise((resolve) => {\n    const binaryString = window.atob(\n      canvas\n        .toDataURL(\n          options.type ? options.type : undefined,\n          options.quality ? options.quality : undefined,\n        )\n        .split(',')[1],\n    )\n    const len = binaryString.length\n    const binaryArray = new Uint8Array(len)\n\n    for (let i = 0; i < len; i += 1) {\n      binaryArray[i] = binaryString.charCodeAt(i)\n    }\n\n    resolve(\n      new Blob([binaryArray], {\n        type: options.type ? options.type : 'image/png',\n      }),\n    )\n  })\n}\n\nexport function createImage(url: string): Promise<HTMLImageElement> {\n  return new Promise((resolve, reject) => {\n    const img = new Image()\n    img.onload = () => {\n      img.decode().then(() => {\n        requestAnimationFrame(() => resolve(img))\n      })\n    }\n    img.onerror = reject\n    img.crossOrigin = 'anonymous'\n    img.decoding = 'async'\n    img.src = url\n  })\n}\n\nexport async function svgToDataURL(svg: SVGElement): Promise<string> {\n  return Promise.resolve()\n    .then(() => new XMLSerializer().serializeToString(svg))\n    .then(encodeURIComponent)\n    .then((html) => `data:image/svg+xml;charset=utf-8,${html}`)\n}\n\nexport async function nodeToDataURL(\n  node: HTMLElement,\n  width: number,\n  height: number,\n): Promise<string> {\n  const xmlns = 'http://www.w3.org/2000/svg'\n  const svg = document.createElementNS(xmlns, 'svg')\n  const foreignObject = document.createElementNS(xmlns, 'foreignObject')\n\n  svg.setAttribute('width', `${width}`)\n  svg.setAttribute('height', `${height}`)\n  svg.setAttribute('viewBox', `0 0 ${width} ${height}`)\n\n  foreignObject.setAttribute('width', '100%')\n  foreignObject.setAttribute('height', '100%')\n  foreignObject.setAttribute('x', '0')\n  foreignObject.setAttribute('y', '0')\n  foreignObject.setAttribute('externalResourcesRequired', 'true')\n\n  svg.appendChild(foreignObject)\n  foreignObject.appendChild(node)\n  return svgToDataURL(svg)\n}\n\nexport const isInstanceOfElement = <\n  T extends typeof Element | typeof HTMLElement | typeof SVGImageElement,\n>(\n  node: Element | HTMLElement | SVGImageElement,\n  instance: T,\n): node is T['prototype'] => {\n  if (node instanceof instance) return true\n\n  const nodePrototype = Object.getPrototypeOf(node)\n\n  if (nodePrototype === null) return false\n\n  return (\n    nodePrototype.constructor.name === instance.name ||\n    isInstanceOfElement(nodePrototype, instance)\n  )\n}\n", "import type { Options } from './types'\nimport { uuid, getStyleProperties } from './util'\n\ntype Pseudo = ':before' | ':after'\n\nfunction formatCSSText(style: CSSStyleDeclaration) {\n  const content = style.getPropertyValue('content')\n  return `${style.cssText} content: '${content.replace(/'|\"/g, '')}';`\n}\n\nfunction formatCSSProperties(style: CSSStyleDeclaration, options: Options) {\n  return getStyleProperties(options)\n    .map((name) => {\n      const value = style.getPropertyValue(name)\n      const priority = style.getPropertyPriority(name)\n\n      return `${name}: ${value}${priority ? ' !important' : ''};`\n    })\n    .join(' ')\n}\n\nfunction getPseudoElementStyle(\n  className: string,\n  pseudo: Pseudo,\n  style: CSSStyleDeclaration,\n  options: Options,\n): Text {\n  const selector = `.${className}:${pseudo}`\n  const cssText = style.cssText\n    ? formatCSSText(style)\n    : formatCSSProperties(style, options)\n\n  return document.createTextNode(`${selector}{${cssText}}`)\n}\n\nfunction clonePseudoElement<T extends HTMLElement>(\n  nativeNode: T,\n  clonedNode: T,\n  pseudo: Pseudo,\n  options: Options,\n) {\n  const style = window.getComputedStyle(nativeNode, pseudo)\n  const content = style.getPropertyValue('content')\n  if (content === '' || content === 'none') {\n    return\n  }\n\n  const className = uuid()\n  try {\n    clonedNode.className = `${clonedNode.className} ${className}`\n  } catch (err) {\n    return\n  }\n\n  const styleElement = document.createElement('style')\n  styleElement.appendChild(\n    getPseudoElementStyle(className, pseudo, style, options),\n  )\n  clonedNode.appendChild(styleElement)\n}\n\nexport function clonePseudoElements<T extends HTMLElement>(\n  nativeNode: T,\n  clonedNode: T,\n  options: Options,\n) {\n  clonePseudoElement(nativeNode, clonedNode, ':before', options)\n  clonePseudoElement(nativeNode, clonedNode, ':after', options)\n}\n", "const WOFF = 'application/font-woff'\nconst JPEG = 'image/jpeg'\nconst mimes: { [key: string]: string } = {\n  woff: WOFF,\n  woff2: WOFF,\n  ttf: 'application/font-truetype',\n  eot: 'application/vnd.ms-fontobject',\n  png: 'image/png',\n  jpg: JPEG,\n  jpeg: JPEG,\n  gif: 'image/gif',\n  tiff: 'image/tiff',\n  svg: 'image/svg+xml',\n  webp: 'image/webp',\n}\n\nfunction getExtension(url: string): string {\n  const match = /\\.([^./]*?)$/g.exec(url)\n  return match ? match[1] : ''\n}\n\nexport function getMimeType(url: string): string {\n  const extension = getExtension(url).toLowerCase()\n  return mimes[extension] || ''\n}\n", "import { Options } from './types'\n\nfunction getContentFromDataUrl(dataURL: string) {\n  return dataURL.split(/,/)[1]\n}\n\nexport function isDataUrl(url: string) {\n  return url.search(/^(data:)/) !== -1\n}\n\nexport function makeDataUrl(content: string, mimeType: string) {\n  return `data:${mimeType};base64,${content}`\n}\n\nexport async function fetchAsDataURL<T>(\n  url: string,\n  init: RequestInit | undefined,\n  process: (data: { result: string; res: Response }) => T,\n): Promise<T> {\n  const res = await fetch(url, init)\n  if (res.status === 404) {\n    throw new Error(`Resource \"${res.url}\" not found`)\n  }\n  const blob = await res.blob()\n  return new Promise<T>((resolve, reject) => {\n    const reader = new FileReader()\n    reader.onerror = reject\n    reader.onloadend = () => {\n      try {\n        resolve(process({ res, result: reader.result as string }))\n      } catch (error) {\n        reject(error)\n      }\n    }\n\n    reader.readAsDataURL(blob)\n  })\n}\n\nconst cache: { [url: string]: string } = {}\n\nfunction getCacheKey(\n  url: string,\n  contentType: string | undefined,\n  includeQueryParams: boolean | undefined,\n) {\n  let key = url.replace(/\\?.*/, '')\n\n  if (includeQueryParams) {\n    key = url\n  }\n\n  // font resource\n  if (/ttf|otf|eot|woff2?/i.test(key)) {\n    key = key.replace(/.*\\//, '')\n  }\n\n  return contentType ? `[${contentType}]${key}` : key\n}\n\nexport async function resourceToDataURL(\n  resourceUrl: string,\n  contentType: string | undefined,\n  options: Options,\n) {\n  const cacheKey = getCacheKey(\n    resourceUrl,\n    contentType,\n    options.includeQueryParams,\n  )\n\n  if (cache[cacheKey] != null) {\n    return cache[cacheKey]\n  }\n\n  // ref: https://developer.mozilla.org/en/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest#Bypassing_the_cache\n  if (options.cacheBust) {\n    // eslint-disable-next-line no-param-reassign\n    resourceUrl += (/\\?/.test(resourceUrl) ? '&' : '?') + new Date().getTime()\n  }\n\n  let dataURL: string\n  try {\n    const content = await fetchAsDataURL(\n      resourceUrl,\n      options.fetchRequestInit,\n      ({ res, result }) => {\n        if (!contentType) {\n          // eslint-disable-next-line no-param-reassign\n          contentType = res.headers.get('Content-Type') || ''\n        }\n        return getContentFromDataUrl(result)\n      },\n    )\n    dataURL = makeDataUrl(content, contentType!)\n  } catch (error) {\n    dataURL = options.imagePlaceholder || ''\n\n    let msg = `Failed to fetch resource: ${resourceUrl}`\n    if (error) {\n      msg = typeof error === 'string' ? error : error.message\n    }\n\n    if (msg) {\n      console.warn(msg)\n    }\n  }\n\n  cache[cacheKey] = dataURL\n  return dataURL\n}\n", "import type { Options } from './types'\nimport { clonePseudoElements } from './clone-pseudos'\nimport {\n  createImage,\n  toArray,\n  isInstanceOfElement,\n  getStyleProperties,\n} from './util'\nimport { getMimeType } from './mimes'\nimport { resourceToDataURL } from './dataurl'\n\nasync function cloneCanvasElement(canvas: HTMLCanvasElement) {\n  const dataURL = canvas.toDataURL()\n  if (dataURL === 'data:,') {\n    return canvas.cloneNode(false) as HTMLCanvasElement\n  }\n  return createImage(dataURL)\n}\n\nasync function cloneVideoElement(video: HTMLVideoElement, options: Options) {\n  if (video.currentSrc) {\n    const canvas = document.createElement('canvas')\n    const ctx = canvas.getContext('2d')\n    canvas.width = video.clientWidth\n    canvas.height = video.clientHeight\n    ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)\n    const dataURL = canvas.toDataURL()\n    return createImage(dataURL)\n  }\n\n  const poster = video.poster\n  const contentType = getMimeType(poster)\n  const dataURL = await resourceToDataURL(poster, contentType, options)\n  return createImage(dataURL)\n}\n\nasync function cloneIFrameElement(iframe: HTMLIFrameElement, options: Options) {\n  try {\n    if (iframe?.contentDocument?.body) {\n      return (await cloneNode(\n        iframe.contentDocument.body,\n        options,\n        true,\n      )) as HTMLBodyElement\n    }\n  } catch {\n    // Failed to clone iframe\n  }\n\n  return iframe.cloneNode(false) as HTMLIFrameElement\n}\n\nasync function cloneSingleNode<T extends HTMLElement>(\n  node: T,\n  options: Options,\n): Promise<HTMLElement> {\n  if (isInstanceOfElement(node, HTMLCanvasElement)) {\n    return cloneCanvasElement(node)\n  }\n\n  if (isInstanceOfElement(node, HTMLVideoElement)) {\n    return cloneVideoElement(node, options)\n  }\n\n  if (isInstanceOfElement(node, HTMLIFrameElement)) {\n    return cloneIFrameElement(node, options)\n  }\n\n  return node.cloneNode(isSVGElement(node)) as T\n}\n\nconst isSlotElement = (node: HTMLElement): node is HTMLSlotElement =>\n  node.tagName != null && node.tagName.toUpperCase() === 'SLOT'\n\nconst isSVGElement = (node: HTMLElement): node is HTMLSlotElement =>\n  node.tagName != null && node.tagName.toUpperCase() === 'SVG'\n\nasync function cloneChildren<T extends HTMLElement>(\n  nativeNode: T,\n  clonedNode: T,\n  options: Options,\n): Promise<T> {\n  if (isSVGElement(clonedNode)) {\n    return clonedNode\n  }\n\n  let children: T[] = []\n\n  if (isSlotElement(nativeNode) && nativeNode.assignedNodes) {\n    children = toArray<T>(nativeNode.assignedNodes())\n  } else if (\n    isInstanceOfElement(nativeNode, HTMLIFrameElement) &&\n    nativeNode.contentDocument?.body\n  ) {\n    children = toArray<T>(nativeNode.contentDocument.body.childNodes)\n  } else {\n    children = toArray<T>((nativeNode.shadowRoot ?? nativeNode).childNodes)\n  }\n\n  if (\n    children.length === 0 ||\n    isInstanceOfElement(nativeNode, HTMLVideoElement)\n  ) {\n    return clonedNode\n  }\n\n  await children.reduce(\n    (deferred, child) =>\n      deferred\n        .then(() => cloneNode(child, options))\n        .then((clonedChild: HTMLElement | null) => {\n          if (clonedChild) {\n            clonedNode.appendChild(clonedChild)\n          }\n        }),\n    Promise.resolve(),\n  )\n\n  return clonedNode\n}\n\nfunction cloneCSSStyle<T extends HTMLElement>(\n  nativeNode: T,\n  clonedNode: T,\n  options: Options,\n) {\n  const targetStyle = clonedNode.style\n  if (!targetStyle) {\n    return\n  }\n\n  const sourceStyle = window.getComputedStyle(nativeNode)\n  if (sourceStyle.cssText) {\n    targetStyle.cssText = sourceStyle.cssText\n    targetStyle.transformOrigin = sourceStyle.transformOrigin\n  } else {\n    getStyleProperties(options).forEach((name) => {\n      let value = sourceStyle.getPropertyValue(name)\n      if (name === 'font-size' && value.endsWith('px')) {\n        const reducedFont =\n          Math.floor(parseFloat(value.substring(0, value.length - 2))) - 0.1\n        value = `${reducedFont}px`\n      }\n\n      if (\n        isInstanceOfElement(nativeNode, HTMLIFrameElement) &&\n        name === 'display' &&\n        value === 'inline'\n      ) {\n        value = 'block'\n      }\n\n      if (name === 'd' && clonedNode.getAttribute('d')) {\n        value = `path(${clonedNode.getAttribute('d')})`\n      }\n\n      targetStyle.setProperty(\n        name,\n        value,\n        sourceStyle.getPropertyPriority(name),\n      )\n    })\n  }\n}\n\nfunction cloneInputValue<T extends HTMLElement>(nativeNode: T, clonedNode: T) {\n  if (isInstanceOfElement(nativeNode, HTMLTextAreaElement)) {\n    clonedNode.innerHTML = nativeNode.value\n  }\n\n  if (isInstanceOfElement(nativeNode, HTMLInputElement)) {\n    clonedNode.setAttribute('value', nativeNode.value)\n  }\n}\n\nfunction cloneSelectValue<T extends HTMLElement>(nativeNode: T, clonedNode: T) {\n  if (isInstanceOfElement(nativeNode, HTMLSelectElement)) {\n    const clonedSelect = clonedNode as any as HTMLSelectElement\n    const selectedOption = Array.from(clonedSelect.children).find(\n      (child) => nativeNode.value === child.getAttribute('value'),\n    )\n\n    if (selectedOption) {\n      selectedOption.setAttribute('selected', '')\n    }\n  }\n}\n\nfunction decorate<T extends HTMLElement>(\n  nativeNode: T,\n  clonedNode: T,\n  options: Options,\n): T {\n  if (isInstanceOfElement(clonedNode, Element)) {\n    cloneCSSStyle(nativeNode, clonedNode, options)\n    clonePseudoElements(nativeNode, clonedNode, options)\n    cloneInputValue(nativeNode, clonedNode)\n    cloneSelectValue(nativeNode, clonedNode)\n  }\n\n  return clonedNode\n}\n\nasync function ensureSVGSymbols<T extends HTMLElement>(\n  clone: T,\n  options: Options,\n) {\n  const uses = clone.querySelectorAll ? clone.querySelectorAll('use') : []\n  if (uses.length === 0) {\n    return clone\n  }\n\n  const processedDefs: { [key: string]: HTMLElement } = {}\n  for (let i = 0; i < uses.length; i++) {\n    const use = uses[i]\n    const id = use.getAttribute('xlink:href')\n    if (id) {\n      const exist = clone.querySelector(id)\n      const definition = document.querySelector(id) as HTMLElement\n      if (!exist && definition && !processedDefs[id]) {\n        // eslint-disable-next-line no-await-in-loop\n        processedDefs[id] = (await cloneNode(definition, options, true))!\n      }\n    }\n  }\n\n  const nodes = Object.values(processedDefs)\n  if (nodes.length) {\n    const ns = 'http://www.w3.org/1999/xhtml'\n    const svg = document.createElementNS(ns, 'svg')\n    svg.setAttribute('xmlns', ns)\n    svg.style.position = 'absolute'\n    svg.style.width = '0'\n    svg.style.height = '0'\n    svg.style.overflow = 'hidden'\n    svg.style.display = 'none'\n\n    const defs = document.createElementNS(ns, 'defs')\n    svg.appendChild(defs)\n\n    for (let i = 0; i < nodes.length; i++) {\n      defs.appendChild(nodes[i])\n    }\n\n    clone.appendChild(svg)\n  }\n\n  return clone\n}\n\nexport async function cloneNode<T extends HTMLElement>(\n  node: T,\n  options: Options,\n  isRoot?: boolean,\n): Promise<T | null> {\n  if (!isRoot && options.filter && !options.filter(node)) {\n    return null\n  }\n\n  return Promise.resolve(node)\n    .then((clonedNode) => cloneSingleNode(clonedNode, options) as Promise<T>)\n    .then((clonedNode) => cloneChildren(node, clonedNode, options))\n    .then((clonedNode) => decorate(node, clonedNode, options))\n    .then((clonedNode) => ensureSVGSymbols(clonedNode, options))\n}\n", "import { Options } from './types'\nimport { resolveUrl } from './util'\nimport { getMimeType } from './mimes'\nimport { isDataUrl, makeDataUrl, resourceToDataURL } from './dataurl'\n\nconst URL_REGEX = /url\\((['\"]?)([^'\"]+?)\\1\\)/g\nconst URL_WITH_FORMAT_REGEX = /url\\([^)]+\\)\\s*format\\(([\"']?)([^\"']+)\\1\\)/g\nconst FONT_SRC_REGEX = /src:\\s*(?:url\\([^)]+\\)\\s*format\\([^)]+\\)[,;]\\s*)+/g\n\nfunction toRegex(url: string): RegExp {\n  // eslint-disable-next-line no-useless-escape\n  const escaped = url.replace(/([.*+?^${}()|\\[\\]\\/\\\\])/g, '\\\\$1')\n  return new RegExp(`(url\\\\(['\"]?)(${escaped})(['\"]?\\\\))`, 'g')\n}\n\nexport function parseURLs(cssText: string): string[] {\n  const urls: string[] = []\n\n  cssText.replace(URL_REGEX, (raw, quotation, url) => {\n    urls.push(url)\n    return raw\n  })\n\n  return urls.filter((url) => !isDataUrl(url))\n}\n\nexport async function embed(\n  cssText: string,\n  resourceURL: string,\n  baseURL: string | null,\n  options: Options,\n  getContentFromUrl?: (url: string) => Promise<string>,\n): Promise<string> {\n  try {\n    const resolvedURL = baseURL ? resolveUrl(resourceURL, baseURL) : resourceURL\n    const contentType = getMimeType(resourceURL)\n    let dataURL: string\n    if (getContentFromUrl) {\n      const content = await getContentFromUrl(resolvedURL)\n      dataURL = makeDataUrl(content, contentType)\n    } else {\n      dataURL = await resourceToDataURL(resolvedURL, contentType, options)\n    }\n    return cssText.replace(toRegex(resourceURL), `$1${dataURL}$3`)\n  } catch (error) {\n    // pass\n  }\n  return cssText\n}\n\nfunction filterPreferredFontFormat(\n  str: string,\n  { preferredFontFormat }: Options,\n): string {\n  return !preferredFontFormat\n    ? str\n    : str.replace(FONT_SRC_REGEX, (match: string) => {\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n          const [src, , format] = URL_WITH_FORMAT_REGEX.exec(match) || []\n          if (!format) {\n            return ''\n          }\n\n          if (format === preferredFontFormat) {\n            return `src: ${src};`\n          }\n        }\n      })\n}\n\nexport function shouldEmbed(url: string): boolean {\n  return url.search(URL_REGEX) !== -1\n}\n\nexport async function embedResources(\n  cssText: string,\n  baseUrl: string | null,\n  options: Options,\n): Promise<string> {\n  if (!shouldEmbed(cssText)) {\n    return cssText\n  }\n\n  const filteredCSSText = filterPreferredFontFormat(cssText, options)\n  const urls = parseURLs(filteredCSSText)\n  return urls.reduce(\n    (deferred, url) =>\n      deferred.then((css) => embed(css, url, baseUrl, options)),\n    Promise.resolve(filteredCSSText),\n  )\n}\n", "import { Options } from './types'\nimport { embedResources } from './embed-resources'\nimport { toArray, isInstanceOfElement } from './util'\nimport { isDataUrl, resourceToDataURL } from './dataurl'\nimport { getMimeType } from './mimes'\n\nasync function embedProp(\n  propName: string,\n  node: HTMLElement,\n  options: Options,\n) {\n  const propValue = node.style?.getPropertyValue(propName)\n  if (propValue) {\n    const cssString = await embedResources(propValue, null, options)\n    node.style.setProperty(\n      propName,\n      cssString,\n      node.style.getPropertyPriority(propName),\n    )\n    return true\n  }\n  return false\n}\n\nasync function embedBackground<T extends HTMLElement>(\n  clonedNode: T,\n  options: Options,\n) {\n  ;(await embedProp('background', clonedNode, options)) ||\n    (await embedProp('background-image', clonedNode, options))\n  ;(await embed<PERSON>rop('mask', clonedNode, options)) ||\n    (await embedProp('-webkit-mask', clonedNode, options)) ||\n    (await embedProp('mask-image', clonedNode, options)) ||\n    (await embedProp('-webkit-mask-image', clonedNode, options))\n}\n\nasync function embedImageNode<T extends HTMLElement | SVGImageElement>(\n  clonedNode: T,\n  options: Options,\n) {\n  const isImageElement = isInstanceOfElement(clonedNode, HTMLImageElement)\n\n  if (\n    !(isImageElement && !isDataUrl(clonedNode.src)) &&\n    !(\n      isInstanceOfElement(clonedNode, SVGImageElement) &&\n      !isDataUrl(clonedNode.href.baseVal)\n    )\n  ) {\n    return\n  }\n\n  const url = isImageElement ? clonedNode.src : clonedNode.href.baseVal\n\n  const dataURL = await resourceToDataURL(url, getMimeType(url), options)\n  await new Promise((resolve, reject) => {\n    clonedNode.onload = resolve\n    clonedNode.onerror = options.onImageErrorHandler\n      ? (...attributes) => {\n          try {\n            resolve(options.onImageErrorHandler!(...attributes))\n          } catch (error) {\n            reject(error)\n          }\n        }\n      : reject\n\n    const image = clonedNode as HTMLImageElement\n    if (image.decode) {\n      image.decode = resolve as any\n    }\n\n    if (image.loading === 'lazy') {\n      image.loading = 'eager'\n    }\n\n    if (isImageElement) {\n      clonedNode.srcset = ''\n      clonedNode.src = dataURL\n    } else {\n      clonedNode.href.baseVal = dataURL\n    }\n  })\n}\n\nasync function embedChildren<T extends HTMLElement>(\n  clonedNode: T,\n  options: Options,\n) {\n  const children = toArray<HTMLElement>(clonedNode.childNodes)\n  const deferreds = children.map((child) => embedImages(child, options))\n  await Promise.all(deferreds).then(() => clonedNode)\n}\n\nexport async function embedImages<T extends HTMLElement>(\n  clonedNode: T,\n  options: Options,\n) {\n  if (isInstanceOfElement(clonedNode, Element)) {\n    await embedBackground(clonedNode, options)\n    await embedImageNode(clonedNode, options)\n    await embedChildren(clonedNode, options)\n  }\n}\n", "import type { Options } from './types'\n\nexport function applyStyle<T extends HTMLElement>(\n  node: T,\n  options: Options,\n): T {\n  const { style } = node\n\n  if (options.backgroundColor) {\n    style.backgroundColor = options.backgroundColor\n  }\n\n  if (options.width) {\n    style.width = `${options.width}px`\n  }\n\n  if (options.height) {\n    style.height = `${options.height}px`\n  }\n\n  const manual = options.style\n  if (manual != null) {\n    Object.keys(manual).forEach((key: any) => {\n      style[key] = manual[key] as string\n    })\n  }\n\n  return node\n}\n", "import type { Options } from './types'\nimport { toArray } from './util'\nimport { fetchAsDataURL } from './dataurl'\nimport { shouldEmbed, embedResources } from './embed-resources'\n\ninterface Metadata {\n  url: string\n  cssText: string\n}\n\nconst cssFetchCache: { [href: string]: Metadata } = {}\n\nasync function fetchCSS(url: string) {\n  let cache = cssFetchCache[url]\n  if (cache != null) {\n    return cache\n  }\n\n  const res = await fetch(url)\n  const cssText = await res.text()\n  cache = { url, cssText }\n\n  cssFetchCache[url] = cache\n\n  return cache\n}\n\nasync function embedFonts(data: Metadata, options: Options): Promise<string> {\n  let cssText = data.cssText\n  const regexUrl = /url\\([\"']?([^\"')]+)[\"']?\\)/g\n  const fontLocs = cssText.match(/url\\([^)]+\\)/g) || []\n  const loadFonts = fontLocs.map(async (loc: string) => {\n    let url = loc.replace(regexUrl, '$1')\n    if (!url.startsWith('https://')) {\n      url = new URL(url, data.url).href\n    }\n\n    return fetchAsDataURL<[string, string]>(\n      url,\n      options.fetchRequestInit,\n      ({ result }) => {\n        cssText = cssText.replace(loc, `url(${result})`)\n        return [loc, result]\n      },\n    )\n  })\n\n  return Promise.all(loadFonts).then(() => cssText)\n}\n\nfunction parseCSS(source: string) {\n  if (source == null) {\n    return []\n  }\n\n  const result: string[] = []\n  const commentsRegex = /(\\/\\*[\\s\\S]*?\\*\\/)/gi\n  // strip out comments\n  let cssText = source.replace(commentsRegex, '')\n\n  // eslint-disable-next-line prefer-regex-literals\n  const keyframesRegex = new RegExp(\n    '((@.*?keyframes [\\\\s\\\\S]*?){([\\\\s\\\\S]*?}\\\\s*?)})',\n    'gi',\n  )\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    const matches = keyframesRegex.exec(cssText)\n    if (matches === null) {\n      break\n    }\n    result.push(matches[0])\n  }\n  cssText = cssText.replace(keyframesRegex, '')\n\n  const importRegex = /@import[\\s\\S]*?url\\([^)]*\\)[\\s\\S]*?;/gi\n  // to match css & media queries together\n  const combinedCSSRegex =\n    '((\\\\s*?(?:\\\\/\\\\*[\\\\s\\\\S]*?\\\\*\\\\/)?\\\\s*?@media[\\\\s\\\\S]' +\n    '*?){([\\\\s\\\\S]*?)}\\\\s*?})|(([\\\\s\\\\S]*?){([\\\\s\\\\S]*?)})'\n  // unified regex\n  const unifiedRegex = new RegExp(combinedCSSRegex, 'gi')\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    let matches = importRegex.exec(cssText)\n    if (matches === null) {\n      matches = unifiedRegex.exec(cssText)\n      if (matches === null) {\n        break\n      } else {\n        importRegex.lastIndex = unifiedRegex.lastIndex\n      }\n    } else {\n      unifiedRegex.lastIndex = importRegex.lastIndex\n    }\n    result.push(matches[0])\n  }\n\n  return result\n}\n\nasync function getCSSRules(\n  styleSheets: CSSStyleSheet[],\n  options: Options,\n): Promise<CSSStyleRule[]> {\n  const ret: CSSStyleRule[] = []\n  const deferreds: Promise<number | void>[] = []\n\n  // First loop inlines imports\n  styleSheets.forEach((sheet) => {\n    if ('cssRules' in sheet) {\n      try {\n        toArray<CSSRule>(sheet.cssRules || []).forEach((item, index) => {\n          if (item.type === CSSRule.IMPORT_RULE) {\n            let importIndex = index + 1\n            const url = (item as CSSImportRule).href\n            const deferred = fetchCSS(url)\n              .then((metadata) => embedFonts(metadata, options))\n              .then((cssText) =>\n                parseCSS(cssText).forEach((rule) => {\n                  try {\n                    sheet.insertRule(\n                      rule,\n                      rule.startsWith('@import')\n                        ? (importIndex += 1)\n                        : sheet.cssRules.length,\n                    )\n                  } catch (error) {\n                    console.error('Error inserting rule from remote css', {\n                      rule,\n                      error,\n                    })\n                  }\n                }),\n              )\n              .catch((e) => {\n                console.error('Error loading remote css', e.toString())\n              })\n\n            deferreds.push(deferred)\n          }\n        })\n      } catch (e) {\n        const inline =\n          styleSheets.find((a) => a.href == null) || document.styleSheets[0]\n        if (sheet.href != null) {\n          deferreds.push(\n            fetchCSS(sheet.href)\n              .then((metadata) => embedFonts(metadata, options))\n              .then((cssText) =>\n                parseCSS(cssText).forEach((rule) => {\n                  inline.insertRule(rule, inline.cssRules.length)\n                }),\n              )\n              .catch((err: unknown) => {\n                console.error('Error loading remote stylesheet', err)\n              }),\n          )\n        }\n        console.error('Error inlining remote css file', e)\n      }\n    }\n  })\n\n  return Promise.all(deferreds).then(() => {\n    // Second loop parses rules\n    styleSheets.forEach((sheet) => {\n      if ('cssRules' in sheet) {\n        try {\n          toArray<CSSStyleRule>(sheet.cssRules || []).forEach((item) => {\n            ret.push(item)\n          })\n        } catch (e) {\n          console.error(`Error while reading CSS rules from ${sheet.href}`, e)\n        }\n      }\n    })\n\n    return ret\n  })\n}\n\nfunction getWebFontRules(cssRules: CSSStyleRule[]): CSSStyleRule[] {\n  return cssRules\n    .filter((rule) => rule.type === CSSRule.FONT_FACE_RULE)\n    .filter((rule) => shouldEmbed(rule.style.getPropertyValue('src')))\n}\n\nasync function parseWebFontRules<T extends HTMLElement>(\n  node: T,\n  options: Options,\n) {\n  if (node.ownerDocument == null) {\n    throw new Error('Provided element is not within a Document')\n  }\n\n  const styleSheets = toArray<CSSStyleSheet>(node.ownerDocument.styleSheets)\n  const cssRules = await getCSSRules(styleSheets, options)\n\n  return getWebFontRules(cssRules)\n}\n\nfunction normalizeFontFamily(font: string) {\n  return font.trim().replace(/[\"']/g, '')\n}\n\nfunction getUsedFonts(node: HTMLElement) {\n  const fonts = new Set<string>()\n  function traverse(node: HTMLElement) {\n    const fontFamily =\n      node.style.fontFamily || getComputedStyle(node).fontFamily\n    fontFamily.split(',').forEach((font) => {\n      fonts.add(normalizeFontFamily(font))\n    })\n\n    Array.from(node.children).forEach((child) => {\n      if (child instanceof HTMLElement) {\n        traverse(child)\n      }\n    })\n  }\n  traverse(node)\n  return fonts\n}\n\nexport async function getWebFontCSS<T extends HTMLElement>(\n  node: T,\n  options: Options,\n): Promise<string> {\n  const rules = await parseWebFontRules(node, options)\n  const usedFonts = getUsedFonts(node)\n  const cssTexts = await Promise.all(\n    rules\n      .filter((rule) =>\n        usedFonts.has(normalizeFontFamily(rule.style.fontFamily)),\n      )\n      .map((rule) => {\n        const baseUrl = rule.parentStyleSheet\n          ? rule.parentStyleSheet.href\n          : null\n        return embedResources(rule.cssText, baseUrl, options)\n      }),\n  )\n\n  return cssTexts.join('\\n')\n}\n\nexport async function embedWebFonts<T extends HTMLElement>(\n  clonedNode: T,\n  options: Options,\n) {\n  const cssText =\n    options.fontEmbedCSS != null\n      ? options.fontEmbedCSS\n      : options.skipFonts\n      ? null\n      : await getWebFontCSS(clonedNode, options)\n\n  if (cssText) {\n    const styleNode = document.createElement('style')\n    const sytleContent = document.createTextNode(cssText)\n\n    styleNode.appendChild(sytleContent)\n\n    if (clonedNode.firstChild) {\n      clonedNode.insertBefore(styleNode, clonedNode.firstChild)\n    } else {\n      clonedNode.appendChild(styleNode)\n    }\n  }\n}\n", "import { Options } from './types'\nimport { cloneNode } from './clone-node'\nimport { embedImages } from './embed-images'\nimport { applyStyle } from './apply-style'\nimport { embedWebFonts, getWebFontCSS } from './embed-webfonts'\nimport {\n  getImageSize,\n  getPixelRatio,\n  createImage,\n  canvasToBlob,\n  nodeToDataURL,\n  checkCanvasDimensions,\n} from './util'\n\nexport async function toSvg<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<string> {\n  const { width, height } = getImageSize(node, options)\n  const clonedNode = (await cloneNode(node, options, true)) as HTMLElement\n  await embedWebFonts(clonedNode, options)\n  await embedImages(clonedNode, options)\n  applyStyle(clonedNode, options)\n  const datauri = await nodeToDataURL(clonedNode, width, height)\n  return datauri\n}\n\nexport async function toCanvas<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<HTMLCanvasElement> {\n  const { width, height } = getImageSize(node, options)\n  const svg = await toSvg(node, options)\n  const img = await createImage(svg)\n\n  const canvas = document.createElement('canvas')\n  const context = canvas.getContext('2d')!\n  const ratio = options.pixelRatio || getPixelRatio()\n  const canvasWidth = options.canvasWidth || width\n  const canvasHeight = options.canvasHeight || height\n\n  canvas.width = canvasWidth * ratio\n  canvas.height = canvasHeight * ratio\n\n  if (!options.skipAutoScale) {\n    checkCanvasDimensions(canvas)\n  }\n  canvas.style.width = `${canvasWidth}`\n  canvas.style.height = `${canvasHeight}`\n\n  if (options.backgroundColor) {\n    context.fillStyle = options.backgroundColor\n    context.fillRect(0, 0, canvas.width, canvas.height)\n  }\n\n  context.drawImage(img, 0, 0, canvas.width, canvas.height)\n\n  return canvas\n}\n\nexport async function toPixelData<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<Uint8ClampedArray> {\n  const { width, height } = getImageSize(node, options)\n  const canvas = await toCanvas(node, options)\n  const ctx = canvas.getContext('2d')!\n  return ctx.getImageData(0, 0, width, height).data\n}\n\nexport async function toPng<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<string> {\n  const canvas = await toCanvas(node, options)\n  return canvas.toDataURL()\n}\n\nexport async function toJpeg<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<string> {\n  const canvas = await toCanvas(node, options)\n  return canvas.toDataURL('image/jpeg', options.quality || 1)\n}\n\nexport async function toBlob<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<Blob | null> {\n  const canvas = await toCanvas(node, options)\n  const blob = await canvasToBlob(canvas)\n  return blob\n}\n\nexport async function getFontEmbedCSS<T extends HTMLElement>(\n  node: T,\n  options: Options = {},\n): Promise<string> {\n  return getWebFontCSS(node, options)\n}\n"], "mappings": ";;;AAEM,SAAU,WAAW,KAAa,SAAsB;AAE5D,MAAI,IAAI,MAAM,eAAe,GAAG;AAC9B,WAAO;;AAIT,MAAI,IAAI,MAAM,OAAO,GAAG;AACtB,WAAO,OAAO,SAAS,WAAW;;AAIpC,MAAI,IAAI,MAAM,WAAW,GAAG;AAC1B,WAAO;;AAGT,QAAM,MAAM,SAAS,eAAe,mBAAkB;AACtD,QAAM,OAAO,IAAI,cAAc,MAAM;AACrC,QAAM,IAAI,IAAI,cAAc,GAAG;AAE/B,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,KAAK,YAAY,CAAC;AAEtB,MAAI,SAAS;AACX,SAAK,OAAO;;AAGd,IAAE,OAAO;AAET,SAAO,EAAE;AACX;AAEO,IAAM,OAAQ,uBAAK;AAGxB,MAAI,UAAU;AAGd,QAAM,SAAS;;IAEb,QAAS,KAAK,OAAM,IAAK,MAAM,KAAM,GAAG,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE;;AAEjE,SAAO,MAAK;AACV,eAAW;AACX,WAAO,IAAI,OAAM,CAAE,GAAG,OAAO;EAC/B;AACF,GAAE;AASI,SAAU,QAAW,WAAc;AACvC,QAAM,MAAW,CAAA;AAEjB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,QAAI,KAAK,UAAU,CAAC,CAAC;;AAGvB,SAAO;AACT;AAEA,IAAI,aAA8B;AAC5B,SAAU,mBAAmB,UAAmB,CAAA,GAAE;AACtD,MAAI,YAAY;AACd,WAAO;;AAGT,MAAI,QAAQ,wBAAwB;AAClC,iBAAa,QAAQ;AACrB,WAAO;;AAGT,eAAa,QAAQ,OAAO,iBAAiB,SAAS,eAAe,CAAC;AAEtE,SAAO;AACT;AAEA,SAAS,GAAG,MAAmB,eAAqB;AAClD,QAAM,MAAM,KAAK,cAAc,eAAe;AAC9C,QAAM,MAAM,IAAI,iBAAiB,IAAI,EAAE,iBAAiB,aAAa;AACrE,SAAO,MAAM,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC,IAAI;AACnD;AAEA,SAAS,aAAa,MAAiB;AACrC,QAAM,aAAa,GAAG,MAAM,mBAAmB;AAC/C,QAAM,cAAc,GAAG,MAAM,oBAAoB;AACjD,SAAO,KAAK,cAAc,aAAa;AACzC;AAEA,SAAS,cAAc,MAAiB;AACtC,QAAM,YAAY,GAAG,MAAM,kBAAkB;AAC7C,QAAM,eAAe,GAAG,MAAM,qBAAqB;AACnD,SAAO,KAAK,eAAe,YAAY;AACzC;AAEM,SAAU,aAAa,YAAyB,UAAmB,CAAA,GAAE;AACzE,QAAM,QAAQ,QAAQ,SAAS,aAAa,UAAU;AACtD,QAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AAEzD,SAAO,EAAE,OAAO,OAAM;AACxB;AAEM,SAAU,gBAAa;AAC3B,MAAI;AAEJ,MAAI;AACJ,MAAI;AACF,oBAAgB;WACT,GAAG;;AAIZ,QAAM,MACJ,iBAAiB,cAAc,MAC3B,cAAc,IAAI,mBAClB;AACN,MAAI,KAAK;AACP,YAAQ,SAAS,KAAK,EAAE;AACxB,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,cAAQ;;;AAGZ,SAAO,SAAS,OAAO,oBAAoB;AAC7C;AAGA,IAAM,uBAAuB;AAEvB,SAAU,sBAAsB,QAAyB;AAC7D,MACE,OAAO,QAAQ,wBACf,OAAO,SAAS,sBAChB;AACA,QACE,OAAO,QAAQ,wBACf,OAAO,SAAS,sBAChB;AACA,UAAI,OAAO,QAAQ,OAAO,QAAQ;AAChC,eAAO,UAAU,uBAAuB,OAAO;AAC/C,eAAO,QAAQ;aACV;AACL,eAAO,SAAS,uBAAuB,OAAO;AAC9C,eAAO,SAAS;;eAET,OAAO,QAAQ,sBAAsB;AAC9C,aAAO,UAAU,uBAAuB,OAAO;AAC/C,aAAO,QAAQ;WACV;AACL,aAAO,SAAS,uBAAuB,OAAO;AAC9C,aAAO,SAAS;;;AAGtB;AAEM,SAAU,aACd,QACA,UAAmB,CAAA,GAAE;AAErB,MAAI,OAAO,QAAQ;AACjB,WAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,aAAO,OACL,SACA,QAAQ,OAAO,QAAQ,OAAO,aAC9B,QAAQ,UAAU,QAAQ,UAAU,CAAC;IAEzC,CAAC;;AAGH,SAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,UAAM,eAAe,OAAO,KAC1B,OACG,UACC,QAAQ,OAAO,QAAQ,OAAO,QAC9B,QAAQ,UAAU,QAAQ,UAAU,MAAS,EAE9C,MAAM,GAAG,EAAE,CAAC,CAAC;AAElB,UAAM,MAAM,aAAa;AACzB,UAAM,cAAc,IAAI,WAAW,GAAG;AAEtC,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,kBAAY,CAAC,IAAI,aAAa,WAAW,CAAC;;AAG5C,YACE,IAAI,KAAK,CAAC,WAAW,GAAG;MACtB,MAAM,QAAQ,OAAO,QAAQ,OAAO;KACrC,CAAC;EAEN,CAAC;AACH;AAEM,SAAU,YAAY,KAAW;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAM,MAAM,IAAI,MAAK;AACrB,QAAI,SAAS,MAAK;AAChB,UAAI,OAAM,EAAG,KAAK,MAAK;AACrB,8BAAsB,MAAM,QAAQ,GAAG,CAAC;MAC1C,CAAC;IACH;AACA,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,QAAI,WAAW;AACf,QAAI,MAAM;EACZ,CAAC;AACH;AAEA,eAAsB,aAAa,KAAe;AAChD,SAAO,QAAQ,QAAO,EACnB,KAAK,MAAM,IAAI,cAAa,EAAG,kBAAkB,GAAG,CAAC,EACrD,KAAK,kBAAkB,EACvB,KAAK,CAAC,SAAS,oCAAoC,IAAI,EAAE;AAC9D;AAEA,eAAsB,cACpB,MACA,OACA,QAAc;AAEd,QAAM,QAAQ;AACd,QAAM,MAAM,SAAS,gBAAgB,OAAO,KAAK;AACjD,QAAM,gBAAgB,SAAS,gBAAgB,OAAO,eAAe;AAErE,MAAI,aAAa,SAAS,GAAG,KAAK,EAAE;AACpC,MAAI,aAAa,UAAU,GAAG,MAAM,EAAE;AACtC,MAAI,aAAa,WAAW,OAAO,KAAK,IAAI,MAAM,EAAE;AAEpD,gBAAc,aAAa,SAAS,MAAM;AAC1C,gBAAc,aAAa,UAAU,MAAM;AAC3C,gBAAc,aAAa,KAAK,GAAG;AACnC,gBAAc,aAAa,KAAK,GAAG;AACnC,gBAAc,aAAa,6BAA6B,MAAM;AAE9D,MAAI,YAAY,aAAa;AAC7B,gBAAc,YAAY,IAAI;AAC9B,SAAO,aAAa,GAAG;AACzB;AAEO,IAAM,sBAAsB,CAGjC,MACA,aAC0B;AAC1B,MAAI,gBAAgB;AAAU,WAAO;AAErC,QAAM,gBAAgB,OAAO,eAAe,IAAI;AAEhD,MAAI,kBAAkB;AAAM,WAAO;AAEnC,SACE,cAAc,YAAY,SAAS,SAAS,QAC5C,oBAAoB,eAAe,QAAQ;AAE/C;;;AC/PA,SAAS,cAAc,OAA0B;AAC/C,QAAM,UAAU,MAAM,iBAAiB,SAAS;AAChD,SAAO,GAAG,MAAM,OAAO,cAAc,QAAQ,QAAQ,QAAQ,EAAE,CAAC;AAClE;AAEA,SAAS,oBAAoB,OAA4B,SAAgB;AACvE,SAAO,mBAAmB,OAAO,EAC9B,IAAI,CAAC,SAAQ;AACZ,UAAM,QAAQ,MAAM,iBAAiB,IAAI;AACzC,UAAM,WAAW,MAAM,oBAAoB,IAAI;AAE/C,WAAO,GAAG,IAAI,KAAK,KAAK,GAAG,WAAW,gBAAgB,EAAE;EAC1D,CAAC,EACA,KAAK,GAAG;AACb;AAEA,SAAS,sBACP,WACA,QACA,OACA,SAAgB;AAEhB,QAAM,WAAW,IAAI,SAAS,IAAI,MAAM;AACxC,QAAM,UAAU,MAAM,UAClB,cAAc,KAAK,IACnB,oBAAoB,OAAO,OAAO;AAEtC,SAAO,SAAS,eAAe,GAAG,QAAQ,IAAI,OAAO,GAAG;AAC1D;AAEA,SAAS,mBACP,YACA,YACA,QACA,SAAgB;AAEhB,QAAM,QAAQ,OAAO,iBAAiB,YAAY,MAAM;AACxD,QAAM,UAAU,MAAM,iBAAiB,SAAS;AAChD,MAAI,YAAY,MAAM,YAAY,QAAQ;AACxC;;AAGF,QAAM,YAAY,KAAI;AACtB,MAAI;AACF,eAAW,YAAY,GAAG,WAAW,SAAS,IAAI,SAAS;WACpD,KAAK;AACZ;;AAGF,QAAM,eAAe,SAAS,cAAc,OAAO;AACnD,eAAa,YACX,sBAAsB,WAAW,QAAQ,OAAO,OAAO,CAAC;AAE1D,aAAW,YAAY,YAAY;AACrC;AAEM,SAAU,oBACd,YACA,YACA,SAAgB;AAEhB,qBAAmB,YAAY,YAAY,WAAW,OAAO;AAC7D,qBAAmB,YAAY,YAAY,UAAU,OAAO;AAC9D;;;ACpEA,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,QAAmC;EACvC,MAAM;EACN,OAAO;EACP,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;EACN,KAAK;EACL,MAAM;EACN,KAAK;EACL,MAAM;;AAGR,SAAS,aAAa,KAAW;AAC/B,QAAM,QAAQ,gBAAgB,KAAK,GAAG;AACtC,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC5B;AAEM,SAAU,YAAY,KAAW;AACrC,QAAM,YAAY,aAAa,GAAG,EAAE,YAAW;AAC/C,SAAO,MAAM,SAAS,KAAK;AAC7B;;;ACtBA,SAAS,sBAAsB,SAAe;AAC5C,SAAO,QAAQ,MAAM,GAAG,EAAE,CAAC;AAC7B;AAEM,SAAU,UAAU,KAAW;AACnC,SAAO,IAAI,OAAO,UAAU,MAAM;AACpC;AAEM,SAAU,YAAY,SAAiB,UAAgB;AAC3D,SAAO,QAAQ,QAAQ,WAAW,OAAO;AAC3C;AAEA,eAAsB,eACpB,KACA,MACAA,UAAuD;AAEvD,QAAM,MAAM,MAAM,MAAM,KAAK,IAAI;AACjC,MAAI,IAAI,WAAW,KAAK;AACtB,UAAM,IAAI,MAAM,aAAa,IAAI,GAAG,aAAa;;AAEnD,QAAM,OAAO,MAAM,IAAI,KAAI;AAC3B,SAAO,IAAI,QAAW,CAAC,SAAS,WAAU;AACxC,UAAM,SAAS,IAAI,WAAU;AAC7B,WAAO,UAAU;AACjB,WAAO,YAAY,MAAK;AACtB,UAAI;AACF,gBAAQA,SAAQ,EAAE,KAAK,QAAQ,OAAO,OAAgB,CAAE,CAAC;eAClD,OAAO;AACd,eAAO,KAAK;;IAEhB;AAEA,WAAO,cAAc,IAAI;EAC3B,CAAC;AACH;AAEA,IAAM,QAAmC,CAAA;AAEzC,SAAS,YACP,KACA,aACA,oBAAuC;AAEvC,MAAI,MAAM,IAAI,QAAQ,QAAQ,EAAE;AAEhC,MAAI,oBAAoB;AACtB,UAAM;;AAIR,MAAI,sBAAsB,KAAK,GAAG,GAAG;AACnC,UAAM,IAAI,QAAQ,QAAQ,EAAE;;AAG9B,SAAO,cAAc,IAAI,WAAW,IAAI,GAAG,KAAK;AAClD;AAEA,eAAsB,kBACpB,aACA,aACA,SAAgB;AAEhB,QAAM,WAAW,YACf,aACA,aACA,QAAQ,kBAAkB;AAG5B,MAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,WAAO,MAAM,QAAQ;;AAIvB,MAAI,QAAQ,WAAW;AAErB,oBAAgB,KAAK,KAAK,WAAW,IAAI,MAAM,QAAO,oBAAI,KAAI,GAAG,QAAO;;AAG1E,MAAI;AACJ,MAAI;AACF,UAAM,UAAU,MAAM,eACpB,aACA,QAAQ,kBACR,CAAC,EAAE,KAAK,OAAM,MAAM;AAClB,UAAI,CAAC,aAAa;AAEhB,sBAAc,IAAI,QAAQ,IAAI,cAAc,KAAK;;AAEnD,aAAO,sBAAsB,MAAM;IACrC,CAAC;AAEH,cAAU,YAAY,SAAS,WAAY;WACpC,OAAO;AACd,cAAU,QAAQ,oBAAoB;AAEtC,QAAI,MAAM,6BAA6B,WAAW;AAClD,QAAI,OAAO;AACT,YAAM,OAAO,UAAU,WAAW,QAAQ,MAAM;;AAGlD,QAAI,KAAK;AACP,cAAQ,KAAK,GAAG;;;AAIpB,QAAM,QAAQ,IAAI;AAClB,SAAO;AACT;;;ACnGA,eAAe,mBAAmB,QAAyB;AACzD,QAAM,UAAU,OAAO,UAAS;AAChC,MAAI,YAAY,UAAU;AACxB,WAAO,OAAO,UAAU,KAAK;;AAE/B,SAAO,YAAY,OAAO;AAC5B;AAEA,eAAe,kBAAkB,OAAyB,SAAgB;AACxE,MAAI,MAAM,YAAY;AACpB,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,WAAO,QAAQ,MAAM;AACrB,WAAO,SAAS,MAAM;AACtB,YAAG,QAAH,QAAG,SAAA,SAAH,IAAK,UAAU,OAAO,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACvD,UAAMC,WAAU,OAAO,UAAS;AAChC,WAAO,YAAYA,QAAO;;AAG5B,QAAM,SAAS,MAAM;AACrB,QAAM,cAAc,YAAY,MAAM;AACtC,QAAM,UAAU,MAAM,kBAAkB,QAAQ,aAAa,OAAO;AACpE,SAAO,YAAY,OAAO;AAC5B;AAEA,eAAe,mBAAmB,QAA2B,SAAgB;;AAC3E,MAAI;AACF,SAAI,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACjC,aAAQ,MAAM,UACZ,OAAO,gBAAgB,MACvB,SACA,IAAI;;WAGR,IAAM;;AAIR,SAAO,OAAO,UAAU,KAAK;AAC/B;AAEA,eAAe,gBACb,MACA,SAAgB;AAEhB,MAAI,oBAAoB,MAAM,iBAAiB,GAAG;AAChD,WAAO,mBAAmB,IAAI;;AAGhC,MAAI,oBAAoB,MAAM,gBAAgB,GAAG;AAC/C,WAAO,kBAAkB,MAAM,OAAO;;AAGxC,MAAI,oBAAoB,MAAM,iBAAiB,GAAG;AAChD,WAAO,mBAAmB,MAAM,OAAO;;AAGzC,SAAO,KAAK,UAAU,aAAa,IAAI,CAAC;AAC1C;AAEA,IAAM,gBAAgB,CAAC,SACrB,KAAK,WAAW,QAAQ,KAAK,QAAQ,YAAW,MAAO;AAEzD,IAAM,eAAe,CAAC,SACpB,KAAK,WAAW,QAAQ,KAAK,QAAQ,YAAW,MAAO;AAEzD,eAAe,cACb,YACA,YACA,SAAgB;;AAEhB,MAAI,aAAa,UAAU,GAAG;AAC5B,WAAO;;AAGT,MAAI,WAAgB,CAAA;AAEpB,MAAI,cAAc,UAAU,KAAK,WAAW,eAAe;AACzD,eAAW,QAAW,WAAW,cAAa,CAAE;aAEhD,oBAAoB,YAAY,iBAAiB,OACjD,KAAA,WAAW,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,OAC5B;AACA,eAAW,QAAW,WAAW,gBAAgB,KAAK,UAAU;SAC3D;AACL,eAAW,UAAY,KAAA,WAAW,gBAAU,QAAA,OAAA,SAAA,KAAI,YAAY,UAAU;;AAGxE,MACE,SAAS,WAAW,KACpB,oBAAoB,YAAY,gBAAgB,GAChD;AACA,WAAO;;AAGT,QAAM,SAAS,OACb,CAAC,UAAU,UACT,SACG,KAAK,MAAM,UAAU,OAAO,OAAO,CAAC,EACpC,KAAK,CAAC,gBAAmC;AACxC,QAAI,aAAa;AACf,iBAAW,YAAY,WAAW;;EAEtC,CAAC,GACL,QAAQ,QAAO,CAAE;AAGnB,SAAO;AACT;AAEA,SAAS,cACP,YACA,YACA,SAAgB;AAEhB,QAAM,cAAc,WAAW;AAC/B,MAAI,CAAC,aAAa;AAChB;;AAGF,QAAM,cAAc,OAAO,iBAAiB,UAAU;AACtD,MAAI,YAAY,SAAS;AACvB,gBAAY,UAAU,YAAY;AAClC,gBAAY,kBAAkB,YAAY;SACrC;AACL,uBAAmB,OAAO,EAAE,QAAQ,CAAC,SAAQ;AAC3C,UAAI,QAAQ,YAAY,iBAAiB,IAAI;AAC7C,UAAI,SAAS,eAAe,MAAM,SAAS,IAAI,GAAG;AAChD,cAAM,cACJ,KAAK,MAAM,WAAW,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC,CAAC,IAAI;AACjE,gBAAQ,GAAG,WAAW;;AAGxB,UACE,oBAAoB,YAAY,iBAAiB,KACjD,SAAS,aACT,UAAU,UACV;AACA,gBAAQ;;AAGV,UAAI,SAAS,OAAO,WAAW,aAAa,GAAG,GAAG;AAChD,gBAAQ,QAAQ,WAAW,aAAa,GAAG,CAAC;;AAG9C,kBAAY,YACV,MACA,OACA,YAAY,oBAAoB,IAAI,CAAC;IAEzC,CAAC;;AAEL;AAEA,SAAS,gBAAuC,YAAe,YAAa;AAC1E,MAAI,oBAAoB,YAAY,mBAAmB,GAAG;AACxD,eAAW,YAAY,WAAW;;AAGpC,MAAI,oBAAoB,YAAY,gBAAgB,GAAG;AACrD,eAAW,aAAa,SAAS,WAAW,KAAK;;AAErD;AAEA,SAAS,iBAAwC,YAAe,YAAa;AAC3E,MAAI,oBAAoB,YAAY,iBAAiB,GAAG;AACtD,UAAM,eAAe;AACrB,UAAM,iBAAiB,MAAM,KAAK,aAAa,QAAQ,EAAE,KACvD,CAAC,UAAU,WAAW,UAAU,MAAM,aAAa,OAAO,CAAC;AAG7D,QAAI,gBAAgB;AAClB,qBAAe,aAAa,YAAY,EAAE;;;AAGhD;AAEA,SAAS,SACP,YACA,YACA,SAAgB;AAEhB,MAAI,oBAAoB,YAAY,OAAO,GAAG;AAC5C,kBAAc,YAAY,YAAY,OAAO;AAC7C,wBAAoB,YAAY,YAAY,OAAO;AACnD,oBAAgB,YAAY,UAAU;AACtC,qBAAiB,YAAY,UAAU;;AAGzC,SAAO;AACT;AAEA,eAAe,iBACb,OACA,SAAgB;AAEhB,QAAM,OAAO,MAAM,mBAAmB,MAAM,iBAAiB,KAAK,IAAI,CAAA;AACtE,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;;AAGT,QAAM,gBAAgD,CAAA;AACtD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,KAAK,IAAI,aAAa,YAAY;AACxC,QAAI,IAAI;AACN,YAAM,QAAQ,MAAM,cAAc,EAAE;AACpC,YAAM,aAAa,SAAS,cAAc,EAAE;AAC5C,UAAI,CAAC,SAAS,cAAc,CAAC,cAAc,EAAE,GAAG;AAE9C,sBAAc,EAAE,IAAK,MAAM,UAAU,YAAY,SAAS,IAAI;;;;AAKpE,QAAM,QAAQ,OAAO,OAAO,aAAa;AACzC,MAAI,MAAM,QAAQ;AAChB,UAAM,KAAK;AACX,UAAM,MAAM,SAAS,gBAAgB,IAAI,KAAK;AAC9C,QAAI,aAAa,SAAS,EAAE;AAC5B,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,QAAQ;AAClB,QAAI,MAAM,SAAS;AACnB,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,UAAU;AAEpB,UAAM,OAAO,SAAS,gBAAgB,IAAI,MAAM;AAChD,QAAI,YAAY,IAAI;AAEpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,WAAK,YAAY,MAAM,CAAC,CAAC;;AAG3B,UAAM,YAAY,GAAG;;AAGvB,SAAO;AACT;AAEA,eAAsB,UACpB,MACA,SACA,QAAgB;AAEhB,MAAI,CAAC,UAAU,QAAQ,UAAU,CAAC,QAAQ,OAAO,IAAI,GAAG;AACtD,WAAO;;AAGT,SAAO,QAAQ,QAAQ,IAAI,EACxB,KAAK,CAAC,eAAe,gBAAgB,YAAY,OAAO,CAAe,EACvE,KAAK,CAAC,eAAe,cAAc,MAAM,YAAY,OAAO,CAAC,EAC7D,KAAK,CAAC,eAAe,SAAS,MAAM,YAAY,OAAO,CAAC,EACxD,KAAK,CAAC,eAAe,iBAAiB,YAAY,OAAO,CAAC;AAC/D;;;ACnQA,IAAM,YAAY;AAClB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AAEvB,SAAS,QAAQ,KAAW;AAE1B,QAAM,UAAU,IAAI,QAAQ,4BAA4B,MAAM;AAC9D,SAAO,IAAI,OAAO,iBAAiB,OAAO,eAAe,GAAG;AAC9D;AAEM,SAAU,UAAU,SAAe;AACvC,QAAM,OAAiB,CAAA;AAEvB,UAAQ,QAAQ,WAAW,CAAC,KAAK,WAAW,QAAO;AACjD,SAAK,KAAK,GAAG;AACb,WAAO;EACT,CAAC;AAED,SAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC;AAC7C;AAEA,eAAsB,MACpB,SACA,aACA,SACA,SACA,mBAAoD;AAEpD,MAAI;AACF,UAAM,cAAc,UAAU,WAAW,aAAa,OAAO,IAAI;AACjE,UAAM,cAAc,YAAY,WAAW;AAC3C,QAAI;AACJ,QAAI,mBAAmB;AACrB,YAAM,UAAU,MAAM,kBAAkB,WAAW;AACnD,gBAAU,YAAY,SAAS,WAAW;WACrC;AACL,gBAAU,MAAM,kBAAkB,aAAa,aAAa,OAAO;;AAErE,WAAO,QAAQ,QAAQ,QAAQ,WAAW,GAAG,KAAK,OAAO,IAAI;WACtD,OAAO;;AAGhB,SAAO;AACT;AAEA,SAAS,0BACP,KACA,EAAE,oBAAmB,GAAW;AAEhC,SAAO,CAAC,sBACJ,MACA,IAAI,QAAQ,gBAAgB,CAAC,UAAiB;AAE5C,WAAO,MAAM;AACX,YAAM,CAAC,KAAI,EAAG,MAAM,IAAI,sBAAsB,KAAK,KAAK,KAAK,CAAA;AAC7D,UAAI,CAAC,QAAQ;AACX,eAAO;;AAGT,UAAI,WAAW,qBAAqB;AAClC,eAAO,QAAQ,GAAG;;;EAGxB,CAAC;AACP;AAEM,SAAU,YAAY,KAAW;AACrC,SAAO,IAAI,OAAO,SAAS,MAAM;AACnC;AAEA,eAAsB,eACpB,SACA,SACA,SAAgB;AAEhB,MAAI,CAAC,YAAY,OAAO,GAAG;AACzB,WAAO;;AAGT,QAAM,kBAAkB,0BAA0B,SAAS,OAAO;AAClE,QAAM,OAAO,UAAU,eAAe;AACtC,SAAO,KAAK,OACV,CAAC,UAAU,QACT,SAAS,KAAK,CAAC,QAAQ,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,GAC1D,QAAQ,QAAQ,eAAe,CAAC;AAEpC;;;ACrFA,eAAe,UACb,UACA,MACA,SAAgB;;AAEhB,QAAM,aAAY,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB,QAAQ;AACvD,MAAI,WAAW;AACb,UAAM,YAAY,MAAM,eAAe,WAAW,MAAM,OAAO;AAC/D,SAAK,MAAM,YACT,UACA,WACA,KAAK,MAAM,oBAAoB,QAAQ,CAAC;AAE1C,WAAO;;AAET,SAAO;AACT;AAEA,eAAe,gBACb,YACA,SAAgB;AAEhB;AAAC,EAAC,MAAM,UAAU,cAAc,YAAY,OAAO,KAChD,MAAM,UAAU,oBAAoB,YAAY,OAAO;AACzD,EAAC,MAAM,UAAU,QAAQ,YAAY,OAAO,KAC1C,MAAM,UAAU,gBAAgB,YAAY,OAAO,KACnD,MAAM,UAAU,cAAc,YAAY,OAAO,KACjD,MAAM,UAAU,sBAAsB,YAAY,OAAO;AAC9D;AAEA,eAAe,eACb,YACA,SAAgB;AAEhB,QAAM,iBAAiB,oBAAoB,YAAY,gBAAgB;AAEvE,MACE,EAAE,kBAAkB,CAAC,UAAU,WAAW,GAAG,MAC7C,EACE,oBAAoB,YAAY,eAAe,KAC/C,CAAC,UAAU,WAAW,KAAK,OAAO,IAEpC;AACA;;AAGF,QAAM,MAAM,iBAAiB,WAAW,MAAM,WAAW,KAAK;AAE9D,QAAM,UAAU,MAAM,kBAAkB,KAAK,YAAY,GAAG,GAAG,OAAO;AACtE,QAAM,IAAI,QAAQ,CAAC,SAAS,WAAU;AACpC,eAAW,SAAS;AACpB,eAAW,UAAU,QAAQ,sBACzB,IAAI,eAAc;AAChB,UAAI;AACF,gBAAQ,QAAQ,oBAAqB,GAAG,UAAU,CAAC;eAC5C,OAAO;AACd,eAAO,KAAK;;IAEhB,IACA;AAEJ,UAAM,QAAQ;AACd,QAAI,MAAM,QAAQ;AAChB,YAAM,SAAS;;AAGjB,QAAI,MAAM,YAAY,QAAQ;AAC5B,YAAM,UAAU;;AAGlB,QAAI,gBAAgB;AAClB,iBAAW,SAAS;AACpB,iBAAW,MAAM;WACZ;AACL,iBAAW,KAAK,UAAU;;EAE9B,CAAC;AACH;AAEA,eAAe,cACb,YACA,SAAgB;AAEhB,QAAM,WAAW,QAAqB,WAAW,UAAU;AAC3D,QAAM,YAAY,SAAS,IAAI,CAAC,UAAU,YAAY,OAAO,OAAO,CAAC;AACrE,QAAM,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAM,UAAU;AACpD;AAEA,eAAsB,YACpB,YACA,SAAgB;AAEhB,MAAI,oBAAoB,YAAY,OAAO,GAAG;AAC5C,UAAM,gBAAgB,YAAY,OAAO;AACzC,UAAM,eAAe,YAAY,OAAO;AACxC,UAAM,cAAc,YAAY,OAAO;;AAE3C;;;ACrGM,SAAU,WACd,MACA,SAAgB;AAEhB,QAAM,EAAE,MAAK,IAAK;AAElB,MAAI,QAAQ,iBAAiB;AAC3B,UAAM,kBAAkB,QAAQ;;AAGlC,MAAI,QAAQ,OAAO;AACjB,UAAM,QAAQ,GAAG,QAAQ,KAAK;;AAGhC,MAAI,QAAQ,QAAQ;AAClB,UAAM,SAAS,GAAG,QAAQ,MAAM;;AAGlC,QAAM,SAAS,QAAQ;AACvB,MAAI,UAAU,MAAM;AAClB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAY;AACvC,YAAM,GAAG,IAAI,OAAO,GAAG;IACzB,CAAC;;AAGH,SAAO;AACT;;;AClBA,IAAM,gBAA8C,CAAA;AAEpD,eAAe,SAAS,KAAW;AACjC,MAAIC,SAAQ,cAAc,GAAG;AAC7B,MAAIA,UAAS,MAAM;AACjB,WAAOA;;AAGT,QAAM,MAAM,MAAM,MAAM,GAAG;AAC3B,QAAM,UAAU,MAAM,IAAI,KAAI;AAC9B,EAAAA,SAAQ,EAAE,KAAK,QAAO;AAEtB,gBAAc,GAAG,IAAIA;AAErB,SAAOA;AACT;AAEA,eAAe,WAAW,MAAgB,SAAgB;AACxD,MAAI,UAAU,KAAK;AACnB,QAAM,WAAW;AACjB,QAAM,WAAW,QAAQ,MAAM,eAAe,KAAK,CAAA;AACnD,QAAM,YAAY,SAAS,IAAI,OAAO,QAAe;AACnD,QAAI,MAAM,IAAI,QAAQ,UAAU,IAAI;AACpC,QAAI,CAAC,IAAI,WAAW,UAAU,GAAG;AAC/B,YAAM,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE;;AAG/B,WAAO,eACL,KACA,QAAQ,kBACR,CAAC,EAAE,OAAM,MAAM;AACb,gBAAU,QAAQ,QAAQ,KAAK,OAAO,MAAM,GAAG;AAC/C,aAAO,CAAC,KAAK,MAAM;IACrB,CAAC;EAEL,CAAC;AAED,SAAO,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAM,OAAO;AAClD;AAEA,SAAS,SAAS,QAAc;AAC9B,MAAI,UAAU,MAAM;AAClB,WAAO,CAAA;;AAGT,QAAM,SAAmB,CAAA;AACzB,QAAM,gBAAgB;AAEtB,MAAI,UAAU,OAAO,QAAQ,eAAe,EAAE;AAG9C,QAAM,iBAAiB,IAAI,OACzB,oDACA,IAAI;AAIN,SAAO,MAAM;AACX,UAAM,UAAU,eAAe,KAAK,OAAO;AAC3C,QAAI,YAAY,MAAM;AACpB;;AAEF,WAAO,KAAK,QAAQ,CAAC,CAAC;;AAExB,YAAU,QAAQ,QAAQ,gBAAgB,EAAE;AAE5C,QAAM,cAAc;AAEpB,QAAM,mBACJ;AAGF,QAAM,eAAe,IAAI,OAAO,kBAAkB,IAAI;AAGtD,SAAO,MAAM;AACX,QAAI,UAAU,YAAY,KAAK,OAAO;AACtC,QAAI,YAAY,MAAM;AACpB,gBAAU,aAAa,KAAK,OAAO;AACnC,UAAI,YAAY,MAAM;AACpB;aACK;AACL,oBAAY,YAAY,aAAa;;WAElC;AACL,mBAAa,YAAY,YAAY;;AAEvC,WAAO,KAAK,QAAQ,CAAC,CAAC;;AAGxB,SAAO;AACT;AAEA,eAAe,YACb,aACA,SAAgB;AAEhB,QAAM,MAAsB,CAAA;AAC5B,QAAM,YAAsC,CAAA;AAG5C,cAAY,QAAQ,CAAC,UAAS;AAC5B,QAAI,cAAc,OAAO;AACvB,UAAI;AACF,gBAAiB,MAAM,YAAY,CAAA,CAAE,EAAE,QAAQ,CAAC,MAAM,UAAS;AAC7D,cAAI,KAAK,SAAS,QAAQ,aAAa;AACrC,gBAAI,cAAc,QAAQ;AAC1B,kBAAM,MAAO,KAAuB;AACpC,kBAAM,WAAW,SAAS,GAAG,EAC1B,KAAK,CAAC,aAAa,WAAW,UAAU,OAAO,CAAC,EAChD,KAAK,CAAC,YACL,SAAS,OAAO,EAAE,QAAQ,CAAC,SAAQ;AACjC,kBAAI;AACF,sBAAM,WACJ,MACA,KAAK,WAAW,SAAS,IACpB,eAAe,IAChB,MAAM,SAAS,MAAM;uBAEpB,OAAO;AACd,wBAAQ,MAAM,wCAAwC;kBACpD;kBACA;iBACD;;YAEL,CAAC,CAAC,EAEH,MAAM,CAAC,MAAK;AACX,sBAAQ,MAAM,4BAA4B,EAAE,SAAQ,CAAE;YACxD,CAAC;AAEH,sBAAU,KAAK,QAAQ;;QAE3B,CAAC;eACM,GAAG;AACV,cAAM,SACJ,YAAY,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,SAAS,YAAY,CAAC;AACnE,YAAI,MAAM,QAAQ,MAAM;AACtB,oBAAU,KACR,SAAS,MAAM,IAAI,EAChB,KAAK,CAAC,aAAa,WAAW,UAAU,OAAO,CAAC,EAChD,KAAK,CAAC,YACL,SAAS,OAAO,EAAE,QAAQ,CAAC,SAAQ;AACjC,mBAAO,WAAW,MAAM,OAAO,SAAS,MAAM;UAChD,CAAC,CAAC,EAEH,MAAM,CAAC,QAAgB;AACtB,oBAAQ,MAAM,mCAAmC,GAAG;UACtD,CAAC,CAAC;;AAGR,gBAAQ,MAAM,kCAAkC,CAAC;;;EAGvD,CAAC;AAED,SAAO,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAK;AAEtC,gBAAY,QAAQ,CAAC,UAAS;AAC5B,UAAI,cAAc,OAAO;AACvB,YAAI;AACF,kBAAsB,MAAM,YAAY,CAAA,CAAE,EAAE,QAAQ,CAAC,SAAQ;AAC3D,gBAAI,KAAK,IAAI;UACf,CAAC;iBACM,GAAG;AACV,kBAAQ,MAAM,sCAAsC,MAAM,IAAI,IAAI,CAAC;;;IAGzE,CAAC;AAED,WAAO;EACT,CAAC;AACH;AAEA,SAAS,gBAAgB,UAAwB;AAC/C,SAAO,SACJ,OAAO,CAAC,SAAS,KAAK,SAAS,QAAQ,cAAc,EACrD,OAAO,CAAC,SAAS,YAAY,KAAK,MAAM,iBAAiB,KAAK,CAAC,CAAC;AACrE;AAEA,eAAe,kBACb,MACA,SAAgB;AAEhB,MAAI,KAAK,iBAAiB,MAAM;AAC9B,UAAM,IAAI,MAAM,2CAA2C;;AAG7D,QAAM,cAAc,QAAuB,KAAK,cAAc,WAAW;AACzE,QAAM,WAAW,MAAM,YAAY,aAAa,OAAO;AAEvD,SAAO,gBAAgB,QAAQ;AACjC;AAEA,SAAS,oBAAoB,MAAY;AACvC,SAAO,KAAK,KAAI,EAAG,QAAQ,SAAS,EAAE;AACxC;AAEA,SAAS,aAAa,MAAiB;AACrC,QAAM,QAAQ,oBAAI,IAAG;AACrB,WAAS,SAASC,OAAiB;AACjC,UAAM,aACJA,MAAK,MAAM,cAAc,iBAAiBA,KAAI,EAAE;AAClD,eAAW,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAQ;AACrC,YAAM,IAAI,oBAAoB,IAAI,CAAC;IACrC,CAAC;AAED,UAAM,KAAKA,MAAK,QAAQ,EAAE,QAAQ,CAAC,UAAS;AAC1C,UAAI,iBAAiB,aAAa;AAChC,iBAAS,KAAK;;IAElB,CAAC;EACH;AACA,WAAS,IAAI;AACb,SAAO;AACT;AAEA,eAAsB,cACpB,MACA,SAAgB;AAEhB,QAAM,QAAQ,MAAM,kBAAkB,MAAM,OAAO;AACnD,QAAM,YAAY,aAAa,IAAI;AACnC,QAAM,WAAW,MAAM,QAAQ,IAC7B,MACG,OAAO,CAAC,SACP,UAAU,IAAI,oBAAoB,KAAK,MAAM,UAAU,CAAC,CAAC,EAE1D,IAAI,CAAC,SAAQ;AACZ,UAAM,UAAU,KAAK,mBACjB,KAAK,iBAAiB,OACtB;AACJ,WAAO,eAAe,KAAK,SAAS,SAAS,OAAO;EACtD,CAAC,CAAC;AAGN,SAAO,SAAS,KAAK,IAAI;AAC3B;AAEA,eAAsB,cACpB,YACA,SAAgB;AAEhB,QAAM,UACJ,QAAQ,gBAAgB,OACpB,QAAQ,eACR,QAAQ,YACR,OACA,MAAM,cAAc,YAAY,OAAO;AAE7C,MAAI,SAAS;AACX,UAAM,YAAY,SAAS,cAAc,OAAO;AAChD,UAAM,eAAe,SAAS,eAAe,OAAO;AAEpD,cAAU,YAAY,YAAY;AAElC,QAAI,WAAW,YAAY;AACzB,iBAAW,aAAa,WAAW,WAAW,UAAU;WACnD;AACL,iBAAW,YAAY,SAAS;;;AAGtC;;;AClQA,eAAsB,MACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,EAAE,OAAO,OAAM,IAAK,aAAa,MAAM,OAAO;AACpD,QAAM,aAAc,MAAM,UAAU,MAAM,SAAS,IAAI;AACvD,QAAM,cAAc,YAAY,OAAO;AACvC,QAAM,YAAY,YAAY,OAAO;AACrC,aAAW,YAAY,OAAO;AAC9B,QAAM,UAAU,MAAM,cAAc,YAAY,OAAO,MAAM;AAC7D,SAAO;AACT;AAEA,eAAsB,SACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,EAAE,OAAO,OAAM,IAAK,aAAa,MAAM,OAAO;AACpD,QAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AACrC,QAAM,MAAM,MAAM,YAAY,GAAG;AAEjC,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,UAAU,OAAO,WAAW,IAAI;AACtC,QAAM,QAAQ,QAAQ,cAAc,cAAa;AACjD,QAAM,cAAc,QAAQ,eAAe;AAC3C,QAAM,eAAe,QAAQ,gBAAgB;AAE7C,SAAO,QAAQ,cAAc;AAC7B,SAAO,SAAS,eAAe;AAE/B,MAAI,CAAC,QAAQ,eAAe;AAC1B,0BAAsB,MAAM;;AAE9B,SAAO,MAAM,QAAQ,GAAG,WAAW;AACnC,SAAO,MAAM,SAAS,GAAG,YAAY;AAErC,MAAI,QAAQ,iBAAiB;AAC3B,YAAQ,YAAY,QAAQ;AAC5B,YAAQ,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;;AAGpD,UAAQ,UAAU,KAAK,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAExD,SAAO;AACT;AAEA,eAAsB,YACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,EAAE,OAAO,OAAM,IAAK,aAAa,MAAM,OAAO;AACpD,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAC3C,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,SAAO,IAAI,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAC/C;AAEA,eAAsB,MACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAC3C,SAAO,OAAO,UAAS;AACzB;AAEA,eAAsB,OACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAC3C,SAAO,OAAO,UAAU,cAAc,QAAQ,WAAW,CAAC;AAC5D;AAEA,eAAsB,OACpB,MACA,UAAmB,CAAA,GAAE;AAErB,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAC3C,QAAM,OAAO,MAAM,aAAa,MAAM;AACtC,SAAO;AACT;AAEA,eAAsB,gBACpB,MACA,UAAmB,CAAA,GAAE;AAErB,SAAO,cAAc,MAAM,OAAO;AACpC;", "names": ["process", "dataURL", "cache", "node"]}