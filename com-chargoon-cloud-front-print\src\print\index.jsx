import { createRoot } from "react-dom/client";
import { <PERSON>jsViewer } from "../pagedjs";

export const Print = ({ letter, layout }) => {
  return (
    <>
      <PagejsViewer {...{ letter, layout }} />
    </>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  const decoded = decodeURIComponent(window.location.hash.slice(1)); // remove #

  const data = JSON.parse(decoded);

  root.render(<Print letter={data.letter} layout={data.layout} />);
} else {
  console.error(
    'Root element not found. Make sure you have an element with id "root" in your HTML'
  );
}
